# ==========================
# UI Styles and CSS
# ==========================

import streamlit as st
from config import COLORS

def apply_custom_css():
    """Apply custom CSS styling to the Streamlit app."""
    
    st.markdown(f"""
    <style>
    /* Global Styles */
    .main .block-container {{
        background-color: {COLORS["background"]};
        padding-top: 2rem;
    }}

    /* Custom CSS for Modern Calm Theme */
    .stApp {{
        background-color: {COLORS["background"]};
    }}

    /* Improved text contrast */
    .stMarkdown {{
        color: {COLORS["heading"]};
    }}

    /* Better visibility for metric labels */
    .metric-label {{
        color: {COLORS["heading"]} !important;
        font-weight: 600 !important;
    }}

    /* Fix metric card text visibility */
    .stMetric {{
        background-color: rgba(255, 255, 255, 0.9) !important;
        padding: 1rem !important;
        border-radius: 10px !important;
        box-shadow: 0 2px 8px rgba(50, 18, 122, 0.1) !important;
    }}

    .stMetric > div {{
        color: {COLORS["heading"]} !important;
    }}

    .stMetric label {{
        color: {COLORS["heading"]} !important;
        font-weight: 700 !important;
        font-size: 0.9rem !important;
    }}

    .stMetric [data-testid="metric-container"] {{
        background-color: rgba(255, 255, 255, 0.9) !important;
        padding: 1rem !important;
        border-radius: 10px !important;
        border-left: 4px solid {COLORS["button_primary"]} !important;
    }}

    .stMetric [data-testid="metric-container"] > div {{
        color: {COLORS["heading"]} !important;
    }}

    .stMetric [data-testid="metric-container"] label {{
        color: {COLORS["heading"]} !important;
        font-weight: 700 !important;
    }}

    /* Force metric text visibility */
    .stMetric [data-testid="metric-container"] [data-testid="metric-value"] {{
        color: {COLORS["heading"]} !important;
        font-weight: 700 !important;
        font-size: 1.5rem !important;
    }}

    .stMetric [data-testid="metric-container"] [data-testid="metric-delta"] {{
        color: {COLORS["button_primary"]} !important;
        font-weight: 600 !important;
    }}

    /* Alternative approach for metric styling */
    div[data-testid="metric-container"] {{
        background: rgba(255, 255, 255, 0.95) !important;
        padding: 1.2rem !important;
        border-radius: 12px !important;
        border-left: 4px solid {COLORS["button_primary"]} !important;
        box-shadow: 0 4px 12px rgba(50, 18, 122, 0.1) !important;
    }}

    div[data-testid="metric-container"] * {{
        color: {COLORS["heading"]} !important;
    }}

    /* Enhanced readability for cards */
    .modern-card {{
        color: {COLORS["heading"]} !important;
    }}

    /* Improved contrast for data displays */
    .stDataFrame {{
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(50, 18, 122, 0.1);
    }}

    /* Data Cleaning tab text improvements */
    .stSelectbox label {{
        color: {COLORS["heading"]} !important;
        font-weight: 600 !important;
    }}

    .stMultiSelect label {{
        color: {COLORS["heading"]} !important;
        font-weight: 600 !important;
    }}

    .stRadio label {{
        color: {COLORS["heading"]} !important;
        font-weight: 600 !important;
    }}

    .stSlider label {{
        color: {COLORS["heading"]} !important;
        font-weight: 600 !important;
    }}

    /* Improve button styling */
    .stButton > button {{
        background-color: {COLORS["button_primary"]} !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
    }}

    .stButton > button:hover {{
        background-color: {COLORS["heading"]} !important;
        color: white !important;
    }}

    /* Tab Styling */
    .stTabs [data-baseweb="tab-list"] {{
        gap: 8px;
        justify-content: center;
        margin-bottom: 2rem;
        background-color: transparent;
        padding: 1rem;
        border-radius: 20px;
        background: linear-gradient(135deg, {COLORS["secondary_background"]} 0%, {COLORS["card_background"]} 100%);
        box-shadow: 0 8px 32px rgba(0, 130, 146, 0.1);
    }}

    .stTabs [data-baseweb="tab"] {{
        height: 65px;
        padding: 0px 28px;
        background-color: {COLORS["background"]};
        border-radius: 15px;
        color: {COLORS["heading"]};
        font-weight: 600;
        font-size: 1rem;
        border: 2px solid {COLORS["secondary_background"]};
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        margin: 4px;
        box-shadow: 0 4px 12px rgba(50, 18, 122, 0.1);
    }}

    .stTabs [aria-selected="true"] {{
        background: linear-gradient(135deg, {COLORS["button_primary"]} 0%, {COLORS["heading"]} 100%);
        color: white;
        border-color: {COLORS["button_primary"]};
        box-shadow: 0 8px 25px rgba(0, 130, 146, 0.4);
        transform: translateY(-2px);
    }}

    .stTabs [data-baseweb="tab"]:hover {{
        background: linear-gradient(135deg, {COLORS["card_background"]} 0%, {COLORS["secondary_background"]} 100%);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(179, 68, 108, 0.2);
        border-color: {COLORS["accent"]};
    }}

    /* Title Styling */
    .main-title {{
        background: linear-gradient(135deg, {COLORS["heading"]} 0%, {COLORS["button_primary"]} 50%, {COLORS["accent"]} 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 3.5rem;
        font-weight: 800;
        text-align: center;
        margin-bottom: 1rem;
        letter-spacing: -0.02em;
    }}

    .subtitle {{
        color: {COLORS["heading"]};
        font-size: 1.3rem;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: 500;
        opacity: 0.8;
    }}

    /* Card Styles */
    .modern-card {{
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 6px 24px rgba(50, 18, 122, 0.08);
        border: 1px solid rgba(227, 204, 220, 0.3);
        transition: all 0.3s ease;
    }}

    .modern-card:hover {{
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 130, 146, 0.15);
    }}

    /* Button Styles */
    .stButton > button {{
        background: linear-gradient(135deg, {COLORS["button_primary"]} 0%, {COLORS["accent"]} 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 130, 146, 0.3);
    }}

    .stButton > button:hover {{
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(179, 68, 108, 0.4);
        background: linear-gradient(135deg, {COLORS["accent"]} 0%, {COLORS["heading"]} 100%);
    }}

    /* Metric Cards */
    .metric-card {{
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(227, 204, 220, 0.3) 100%);
        backdrop-filter: blur(15px);
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(205, 224, 225, 0.5);
        box-shadow: 0 8px 32px rgba(50, 18, 122, 0.08);
        transition: all 0.3s ease;
    }}

    .metric-card:hover {{
        transform: translateY(-3px);
        box-shadow: 0 12px 40px rgba(0, 130, 146, 0.15);
    }}
    </style>
    """, unsafe_allow_html=True)

def create_header(title, subtitle):
    """Create a styled header section."""
    return f"""
    <div style="text-align: center; margin-bottom: 3rem; padding: 2rem; background: linear-gradient(135deg, rgba(227, 204, 220, 0.3) 0%, rgba(205, 224, 225, 0.3) 100%); border-radius: 25px; backdrop-filter: blur(10px);">
        <h1 class="main-title">{title}</h1>
        <p class="subtitle">{subtitle}</p>
    </div>
    """

def create_section_header(title, subtitle, icon=""):
    """Create a styled section header."""
    return f"""
    <div style="
        background: linear-gradient(135deg, {COLORS["heading"]} 0%, {COLORS["button_primary"]} 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">{icon} {title}</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">{subtitle}</p>
    </div>
    """

def create_info_card(title, content, icon="", color_scheme="default"):
    """Create a styled information card."""
    color_schemes = {
        "default": f"background: linear-gradient(135deg, {COLORS['card_background']} 0%, {COLORS['secondary_background']} 100%);",
        "primary": f"background: linear-gradient(135deg, {COLORS['heading']} 0%, {COLORS['button_primary']} 100%); color: white;",
        "accent": f"background: linear-gradient(135deg, {COLORS['accent']} 0%, {COLORS['heading']} 100%); color: white;"
    }
    
    bg_style = color_schemes.get(color_scheme, color_schemes["default"])
    
    return f"""
    <div style="
        {bg_style}
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        border-left: 4px solid {COLORS['button_primary']};
    ">
        <h3 style="margin-bottom: 0.5rem; font-weight: 700;">{icon} {title}</h3>
        <p style="opacity: 0.8; margin: 0;">{content}</p>
    </div>
    """
