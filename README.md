# 💻 Laptop Recommendation System

A comprehensive AI-powered laptop recommendation system built with Streamlit, featuring data visualization, preprocessing, machine learning model training, and personalized recommendations.

## 🚀 Features

### 📊 Data Visualization
- Interactive charts and graphs for market analysis
- Brand insights and price analytics
- Hardware specifications analysis
- Display and design feature analysis
- Correlation analysis and pair plots

### 🧹 Data Cleaning & Preprocessing
- Step-by-step interactive preprocessing pipeline
- Missing value handling with multiple strategies
- Categorical encoding (Label & One-Hot)
- Outlier detection and handling
- Feature scaling with multiple methods
- Data quality assessment and download options

### 🤖 Machine Learning
- Train and compare KNN, Random Forest, and SVM models
- Train-test split vs K-fold cross-validation comparison
- Comprehensive model evaluation with accuracy, precision, recall, F1-score
- Automatic best model selection
- Feature importance analysis

### 💻 Smart Recommendations
- AI-powered laptop recommendations using trained models
- Hybrid recommendation system (ML + similarity-based)
- Personalized filtering based on user preferences
- Budget, usage type, and specification-based recommendations
- Detailed recommendation explanations

## 🏗️ Project Structure

```
laptop-recommendation-system/
├── main.py                     # Main Streamlit application
├── config.py                   # Configuration settings and constants
├── data_loader.py             # Data loading and caching functions
├── requirements.txt           # Python dependencies
├── README.md                  # Project documentation
├── ui/                        # User interface components
│   ├── styles.py             # CSS styling and theme
│   ├── welcome_tab.py        # Welcome tab component
│   ├── visualization_tab.py  # Data visualization dashboard
│   ├── cleaning_tab.py       # Data cleaning interface
│   ├── model_tab.py          # Model training interface
│   └── recommendation_tab.py # Recommendation interface
├── core/                      # Core functionality
│   ├── data_processor.py     # Data preprocessing functions
│   ├── model_trainer.py      # ML model training and evaluation
│   └── recommendation_engine.py # Recommendation algorithms
└── utils/                     # Utility functions
    ├── helpers.py            # Helper functions
    └── visualization.py      # Plotting utilities
```

## 🛠️ Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd laptop-recommendation-system
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Prepare your data:**
   - Place your laptop dataset as `laptops.csv` in the project root
   - Ensure the dataset has columns like: Brand, Final Price, RAM, Storage, Screen, CPU, etc.

4. **Run the application:**
   ```bash
   streamlit run main.py
   ```

## 📊 Data Requirements

The system expects a CSV file named `laptops.csv` with the following recommended columns:

- **Brand**: Laptop brand (e.g., Dell, HP, Lenovo)
- **Final Price**: Price in USD
- **RAM**: RAM size in GB
- **Storage**: Storage size in GB
- **Screen**: Screen size in inches
- **CPU**: Processor information
- **GPU**: Graphics card information (optional)
- **Touch**: Touchscreen capability (boolean)
- **Weight**: Weight in kg (optional)

## 🎯 Usage Guide

### 1. Welcome Tab
- Overview of the dataset and platform features
- Key statistics and metrics
- Navigation guide

### 2. Data Visualization
- **Brand Insights**: Market share analysis and brand comparisons
- **Price Analytics**: Price distribution and correlation analysis
- **Hardware Specs**: RAM, storage, and CPU analysis
- **Display & Design**: Screen size and touchscreen analysis
- **Correlations**: Feature correlation matrix and pair plots
- **Market Overview**: Comprehensive market insights

### 3. Data Cleaning
Follow the step-by-step preprocessing pipeline:
- **Step 1**: Handle missing values
- **Step 2**: Encode categorical variables
- **Step 3**: Detect and handle outliers
- **Step 4**: Scale numerical features
- **Step 5**: Review and download processed data

### 4. Model Evaluation
- Select target variable for classification
- Choose evaluation method (Train-Test Split, K-Fold CV, or Both)
- Train KNN, Random Forest, and SVM models
- Compare model performance
- Automatic best model selection

### 5. Laptop Recommendation
- Enter your preferences (usage type, budget, specifications)
- Choose recommendation method (ML-only or Hybrid)
- Get personalized laptop recommendations
- View detailed explanations for each recommendation

## 🎨 Customization

### Color Theme
The application uses a "Modern Calm for Students & Developers" color palette defined in `config.py`:
- Background: #F0EFED
- Cards: #E3CCDC
- Primary buttons: #008292
- Headings: #32127A
- Accents: #B3446C

### Adding New Features
1. **New visualization**: Add functions to `ui/visualization_tab.py`
2. **New preprocessing**: Extend `core/data_processor.py`
3. **New ML models**: Update `core/model_trainer.py`
4. **New recommendation logic**: Modify `core/recommendation_engine.py`

## 🔧 Configuration

Key settings in `config.py`:
- **Page configuration**: Title, icon, layout
- **Data settings**: File paths, cache TTL
- **Model parameters**: Random state, test size, CV folds
- **UI settings**: Display limits, chart dimensions
- **Color scheme**: Complete color palette

## 📈 Performance Tips

1. **Data Size**: For large datasets (>10k rows), consider sampling for visualization
2. **Caching**: The app uses Streamlit's caching for data loading
3. **Memory**: Monitor memory usage with large datasets
4. **Processing**: Preprocessing steps are optimized for interactive use

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following the existing code structure
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is open source and available under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the existing documentation
2. Review the code comments
3. Create an issue with detailed description
4. Include error messages and steps to reproduce

## 🔮 Future Enhancements

- [ ] Add more ML algorithms (XGBoost, Neural Networks)
- [ ] Implement collaborative filtering
- [ ] Add user rating system
- [ ] Include laptop images and detailed specs
- [ ] Add comparison tool for selected laptops
- [ ] Implement user accounts and saved preferences
- [ ] Add real-time price tracking
- [ ] Include laptop reviews and ratings

---

Built with ❤️ using Streamlit, scikit-learn, and modern data science tools.
