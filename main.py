# ==========================
# Main Streamlit Application
# ==========================

import streamlit as st
from config import PAGE_CONFIG
from data_loader import load_data, validate_data
from ui.styles import apply_custom_css, create_header
from ui.welcome_tab import render_welcome_tab
from ui.visualization_tab import render_visualization_tab
from ui.cleaning_tab import render_cleaning_tab
from ui.model_tab import render_model_tab
from ui.recommendation_tab import render_recommendation_tab

def main():
    """Main application function."""
    
    # Set page configuration
    st.set_page_config(**PAGE_CONFIG)
    
    # Apply custom CSS styling
    apply_custom_css()
    
    # Load and validate data
    data = load_data()
    
    if not validate_data(data):
        st.stop()
    
    # Main application header
    st.markdown(create_header(
        "💻 Laptop Recommendation System",
        "Discover your perfect laptop with our AI-powered recommendation platform"
    ), unsafe_allow_html=True)
    
    # Create navigation tabs
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "👋 Welcome",
        "📊 Data Visualization", 
        "🧹 Data Cleaning Insights",
        "🤖 Model Evaluation",
        "💻 Laptop Recommendation"
    ])
    
    # Render tab content
    with tab1:
        render_welcome_tab(data)
    
    with tab2:
        render_visualization_tab(data)
    
    with tab3:
        render_cleaning_tab(data)
    
    with tab4:
        render_model_tab(data)
    
    with tab5:
        render_recommendation_tab(data)

if __name__ == "__main__":
    main()
