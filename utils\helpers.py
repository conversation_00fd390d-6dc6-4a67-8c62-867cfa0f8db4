# ==========================
# Helper Functions and Utilities
# ==========================

import pandas as pd
import numpy as np
import streamlit as st
from datetime import datetime
import json

def format_number(number, decimal_places=2):
    """
    Format a number with proper comma separation and decimal places.
    
    Args:
        number (float): Number to format
        decimal_places (int): Number of decimal places
        
    Returns:
        str: Formatted number string
    """
    if pd.isna(number):
        return "N/A"
    
    if decimal_places == 0:
        return f"{number:,.0f}"
    else:
        return f"{number:,.{decimal_places}f}"

def format_currency(amount, currency_symbol="$"):
    """
    Format a number as currency.
    
    Args:
        amount (float): Amount to format
        currency_symbol (str): Currency symbol
        
    Returns:
        str: Formatted currency string
    """
    if pd.isna(amount):
        return "N/A"
    
    return f"{currency_symbol}{amount:,.0f}"

def format_percentage(value, decimal_places=1):
    """
    Format a decimal as percentage.
    
    Args:
        value (float): Decimal value (0.1 = 10%)
        decimal_places (int): Number of decimal places
        
    Returns:
        str: Formatted percentage string
    """
    if pd.isna(value):
        return "N/A"
    
    return f"{value * 100:.{decimal_places}f}%"

def safe_divide(numerator, denominator, default=0):
    """
    Safely divide two numbers, handling division by zero.
    
    Args:
        numerator (float): Numerator
        denominator (float): Denominator
        default (float): Default value if division by zero
        
    Returns:
        float: Result of division or default value
    """
    if denominator == 0 or pd.isna(denominator) or pd.isna(numerator):
        return default
    
    return numerator / denominator

def get_column_info(data, column_name):
    """
    Get comprehensive information about a column.
    
    Args:
        data (pd.DataFrame): Dataset
        column_name (str): Column name
        
    Returns:
        dict: Column information
    """
    if column_name not in data.columns:
        return {}
    
    col_data = data[column_name]
    
    info = {
        'name': column_name,
        'dtype': str(col_data.dtype),
        'total_count': len(col_data),
        'non_null_count': col_data.count(),
        'null_count': col_data.isnull().sum(),
        'null_percentage': (col_data.isnull().sum() / len(col_data)) * 100,
        'unique_count': col_data.nunique(),
        'memory_usage': col_data.memory_usage(deep=True)
    }
    
    # Add type-specific information
    if col_data.dtype in ['int64', 'float64']:
        info.update({
            'min_value': col_data.min(),
            'max_value': col_data.max(),
            'mean_value': col_data.mean(),
            'median_value': col_data.median(),
            'std_value': col_data.std(),
            'has_outliers': detect_outliers_simple(col_data)
        })
    elif col_data.dtype == 'object':
        value_counts = col_data.value_counts()
        info.update({
            'most_frequent': value_counts.index[0] if len(value_counts) > 0 else None,
            'most_frequent_count': value_counts.iloc[0] if len(value_counts) > 0 else 0,
            'sample_values': col_data.dropna().unique()[:5].tolist()
        })
    
    return info

def detect_outliers_simple(series, method='IQR'):
    """
    Simple outlier detection for a numerical series.
    
    Args:
        series (pd.Series): Numerical series
        method (str): Detection method ('IQR' or 'Z-Score')
        
    Returns:
        bool: True if outliers detected, False otherwise
    """
    if series.dtype not in ['int64', 'float64'] or len(series) < 4:
        return False
    
    clean_series = series.dropna()
    
    if method == 'IQR':
        Q1 = clean_series.quantile(0.25)
        Q3 = clean_series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = (clean_series < lower_bound) | (clean_series > upper_bound)
    elif method == 'Z-Score':
        z_scores = np.abs((clean_series - clean_series.mean()) / clean_series.std())
        outliers = z_scores > 3
    else:
        return False
    
    return outliers.any()

def create_download_link(data, filename, file_format='csv'):
    """
    Create a download link for data.
    
    Args:
        data (pd.DataFrame): Data to download
        filename (str): Filename without extension
        file_format (str): File format ('csv', 'excel', 'json')
        
    Returns:
        tuple: (data_bytes, mime_type, full_filename)
    """
    if file_format.lower() == 'csv':
        data_bytes = data.to_csv(index=False).encode('utf-8')
        mime_type = 'text/csv'
        full_filename = f"{filename}.csv"
    
    elif file_format.lower() == 'excel':
        import io
        buffer = io.BytesIO()
        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            data.to_excel(writer, sheet_name='Data', index=False)
        data_bytes = buffer.getvalue()
        mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        full_filename = f"{filename}.xlsx"
    
    elif file_format.lower() == 'json':
        data_bytes = data.to_json(orient='records', indent=2).encode('utf-8')
        mime_type = 'application/json'
        full_filename = f"{filename}.json"
    
    else:
        raise ValueError(f"Unsupported file format: {file_format}")
    
    return data_bytes, mime_type, full_filename

def validate_user_input(input_value, input_type, min_value=None, max_value=None):
    """
    Validate user input based on type and constraints.
    
    Args:
        input_value: Input value to validate
        input_type (str): Expected type ('int', 'float', 'str', 'email')
        min_value: Minimum allowed value (for numbers)
        max_value: Maximum allowed value (for numbers)
        
    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        if input_type == 'int':
            value = int(input_value)
            if min_value is not None and value < min_value:
                return False, f"Value must be at least {min_value}"
            if max_value is not None and value > max_value:
                return False, f"Value must be at most {max_value}"
        
        elif input_type == 'float':
            value = float(input_value)
            if min_value is not None and value < min_value:
                return False, f"Value must be at least {min_value}"
            if max_value is not None and value > max_value:
                return False, f"Value must be at most {max_value}"
        
        elif input_type == 'str':
            if not isinstance(input_value, str) or len(input_value.strip()) == 0:
                return False, "Value cannot be empty"
        
        elif input_type == 'email':
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, input_value):
                return False, "Invalid email format"
        
        return True, ""
    
    except (ValueError, TypeError):
        return False, f"Invalid {input_type} value"

def log_user_action(action, details=None):
    """
    Log user actions for analytics (placeholder function).
    
    Args:
        action (str): Action performed
        details (dict): Additional details
    """
    timestamp = datetime.now().isoformat()
    log_entry = {
        'timestamp': timestamp,
        'action': action,
        'details': details or {}
    }
    
    # In a real application, you would save this to a database or file
    # For now, we'll just store it in session state
    if 'user_actions_log' not in st.session_state:
        st.session_state.user_actions_log = []
    
    st.session_state.user_actions_log.append(log_entry)

def get_data_quality_score(data):
    """
    Calculate a data quality score for the dataset.
    
    Args:
        data (pd.DataFrame): Dataset to evaluate
        
    Returns:
        dict: Data quality metrics
    """
    if data.empty:
        return {'overall_score': 0, 'details': {}}
    
    total_cells = len(data) * len(data.columns)
    missing_cells = data.isnull().sum().sum()
    completeness_score = (total_cells - missing_cells) / total_cells
    
    # Check for duplicates
    duplicate_rows = data.duplicated().sum()
    uniqueness_score = (len(data) - duplicate_rows) / len(data)
    
    # Check data type consistency
    consistency_issues = 0
    for col in data.columns:
        if data[col].dtype == 'object':
            # Check for mixed types in object columns
            sample_types = set(type(x).__name__ for x in data[col].dropna().head(100))
            if len(sample_types) > 1:
                consistency_issues += 1
    
    consistency_score = 1 - (consistency_issues / len(data.columns))
    
    # Overall score (weighted average)
    overall_score = (
        completeness_score * 0.4 +
        uniqueness_score * 0.3 +
        consistency_score * 0.3
    )
    
    return {
        'overall_score': overall_score,
        'details': {
            'completeness': completeness_score,
            'uniqueness': uniqueness_score,
            'consistency': consistency_score,
            'missing_percentage': (missing_cells / total_cells) * 100,
            'duplicate_percentage': (duplicate_rows / len(data)) * 100
        }
    }

def create_progress_bar(current_step, total_steps, step_names=None):
    """
    Create a visual progress bar for multi-step processes.
    
    Args:
        current_step (int): Current step (1-indexed)
        total_steps (int): Total number of steps
        step_names (list): Optional list of step names
        
    Returns:
        str: HTML for progress bar
    """
    if step_names is None:
        step_names = [f"Step {i+1}" for i in range(total_steps)]
    
    progress_percentage = (current_step / total_steps) * 100
    
    html = f"""
    <div style="margin: 1rem 0;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="font-weight: 600;">Progress: {current_step}/{total_steps}</span>
            <span style="font-weight: 600;">{progress_percentage:.0f}%</span>
        </div>
        <div style="background-color: #e0e0e0; border-radius: 10px; height: 20px; overflow: hidden;">
            <div style="background: linear-gradient(90deg, #008292, #32127A); height: 100%; width: {progress_percentage}%; transition: width 0.3s ease;"></div>
        </div>
        <div style="display: flex; justify-content: space-between; margin-top: 0.5rem; font-size: 0.8rem;">
    """
    
    for i, step_name in enumerate(step_names):
        if i < current_step:
            html += f'<span style="color: #008292; font-weight: 600;">✓ {step_name}</span>'
        elif i == current_step - 1:
            html += f'<span style="color: #32127A; font-weight: 600;">→ {step_name}</span>'
        else:
            html += f'<span style="color: #999;">{step_name}</span>'
    
    html += """
        </div>
    </div>
    """
    
    return html

def truncate_text(text, max_length=50, suffix="..."):
    """
    Truncate text to a maximum length.
    
    Args:
        text (str): Text to truncate
        max_length (int): Maximum length
        suffix (str): Suffix to add if truncated
        
    Returns:
        str: Truncated text
    """
    if pd.isna(text):
        return "N/A"
    
    text_str = str(text)
    if len(text_str) <= max_length:
        return text_str
    
    return text_str[:max_length - len(suffix)] + suffix
