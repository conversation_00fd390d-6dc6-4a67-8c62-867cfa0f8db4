# ==========================
# Laptop Recommendation Tab Component
# ==========================

import streamlit as st
import pandas as pd
import numpy as np
from config import COLORS
from ui.styles import create_section_header, create_info_card
from core.recommendation_engine import RecommendationEngine

def render_recommendation_tab(data):
    """Render the laptop recommendation tab."""
    
    # Enhanced header
    st.markdown(create_section_header(
        "💻 Smart Laptop Recommendation",
        "Get personalized laptop recommendations using AI"
    ), unsafe_allow_html=True)

    # Check if model is trained
    if 'best_model_info' not in st.session_state or 'trained_best_model' not in st.session_state:
        st.warning("⚠️ Please train the models first in the 'Model Evaluation & Training' tab.")
        st.info("📍 Go to Tab 4 to train KNN, Random Forest, and SVM models, then return here for recommendations.")
        return

    # Get model info
    model_info = st.session_state['best_model_info']
    trained_model = st.session_state['trained_best_model']

    st.success(f"✅ Using trained **{model_info['model_name']}** model for recommendations!")

    # Show model performance
    render_model_performance(model_info)

    # Initialize recommendation engine
    engine = RecommendationEngine(model_info, trained_model)

    # Recommendation interface
    render_recommendation_interface(engine)

def render_model_performance(model_info):
    """Render model performance metrics."""
    st.markdown("**🏆 Best Model Performance:**")
    model_data = model_info['model_data']

    perf_col1, perf_col2, perf_col3, perf_col4 = st.columns(4)
    with perf_col1:
        accuracy = model_data.get('Accuracy', model_data.get('Mean Accuracy', 'N/A'))
        st.metric("🎯 Accuracy", accuracy)
    with perf_col2:
        precision = model_data.get('Precision', 'N/A')
        st.metric("🎯 Precision", precision)
    with perf_col3:
        recall = model_data.get('Recall', 'N/A')
        st.metric("🎯 Recall", recall)
    with perf_col4:
        f1_score = model_data.get('F1-Score', 'N/A')
        st.metric("🎯 F1-Score", f1_score)

def render_recommendation_interface(engine):
    """Render the recommendation input interface."""
    st.markdown(create_info_card(
        "🎯 Laptop Recommendation System",
        "Enter your preferences to get personalized laptop recommendations"
    ), unsafe_allow_html=True)

    # Get processed data for reference
    processed_data = engine.processed_data

    # User input interface
    st.markdown("**💼 Enter Your Preferences:**")

    # Create input form
    user_preferences = {}

    # Common laptop features for user input
    input_col1, input_col2 = st.columns(2)

    with input_col1:
        # Usage type
        user_preferences['usage_type'] = st.selectbox(
            "🎮 Usage Type:",
            ["Gaming", "Business/Work", "Student/Study", "Graphics/Design", "General Use"],
            key="usage_type"
        )

        # Budget range
        if 'Final Price' in processed_data.columns:
            min_price = float(processed_data['Final Price'].min())
            max_price = float(processed_data['Final Price'].max())
            user_preferences['budget_range'] = st.slider(
                "💰 Budget Range:",
                min_value=min_price,
                max_value=max_price,
                value=(min_price, max_price),
                key="budget_range"
            )

        # RAM preference
        if 'RAM' in processed_data.columns:
            ram_options = sorted(processed_data['RAM'].dropna().unique())
            user_preferences['preferred_ram'] = st.selectbox(
                "💾 Preferred RAM (GB):",
                ram_options,
                index=len(ram_options)//2,
                key="preferred_ram"
            )

    with input_col2:
        # Storage preference
        if 'Storage' in processed_data.columns:
            storage_options = sorted(processed_data['Storage'].dropna().unique())
            user_preferences['preferred_storage'] = st.selectbox(
                "💾 Preferred Storage (GB):",
                storage_options,
                index=len(storage_options)//2,
                key="preferred_storage"
            )

        # Screen size preference
        if 'Screen' in processed_data.columns:
            screen_options = sorted(processed_data['Screen'].dropna().unique())
            user_preferences['preferred_screen'] = st.selectbox(
                "📺 Preferred Screen Size:",
                screen_options,
                index=len(screen_options)//2,
                key="preferred_screen"
            )

        # Brand preference
        if 'Brand' in processed_data.columns:
            brand_options = ['Any'] + sorted(processed_data['Brand'].dropna().unique().tolist())
            user_preferences['preferred_brand'] = st.selectbox(
                "🏷️ Preferred Brand:",
                brand_options,
                key="preferred_brand"
            )

    # Advanced options
    with st.expander("🔧 Advanced Options"):
        col_adv1, col_adv2 = st.columns(2)
        
        with col_adv1:
            num_recommendations = st.slider(
                "📊 Number of Recommendations:",
                min_value=1,
                max_value=10,
                value=5,
                key="num_recommendations"
            )
        
        with col_adv2:
            recommendation_method = st.selectbox(
                "🤖 Recommendation Method:",
                ["ML Model Only", "Hybrid (ML + Similarity)"],
                key="rec_method"
            )

    # Get recommendations button
    if st.button("🚀 Get Laptop Recommendations", type="primary", key="get_recommendations"):
        with st.spinner("🔄 Finding the best laptops for you..."):
            try:
                # Get recommendations based on selected method
                if recommendation_method == "Hybrid (ML + Similarity)":
                    recommendations = engine.get_hybrid_recommendations(
                        user_preferences, num_recommendations
                    )
                else:
                    recommendations = engine.get_recommendations(
                        user_preferences, num_recommendations
                    )

                if len(recommendations) > 0:
                    render_recommendations_display(recommendations, user_preferences, engine)
                else:
                    st.error("❌ No laptops found matching your criteria. Please adjust your preferences.")

            except Exception as e:
                st.error(f"❌ Error generating recommendations: {str(e)}")

def render_recommendations_display(recommendations, user_preferences, engine):
    """Render the recommendations display."""
    st.markdown("### 🏆 Top Laptop Recommendations")

    for idx, (_, laptop) in enumerate(recommendations.iterrows(), 1):
        with st.container():
            # Recommendation header
            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, {COLORS["background"]} 0%, {COLORS["card_background"]} 100%);
                padding: 1.5rem;
                border-radius: 15px;
                margin: 1rem 0;
                border-left: 4px solid {COLORS["button_primary"]};
            ">
                <h4 style="color: {COLORS["heading"]}; margin-bottom: 1rem; font-weight: 700;">
                    🏅 Recommendation #{idx} (Score: {laptop['Recommendation_Score']:.3f})
                </h4>
            </div>
            """, unsafe_allow_html=True)

            # Display laptop details
            render_laptop_details(laptop, idx)

            # Show recommendation explanation
            explanations = engine.explain_recommendation(laptop, user_preferences)
            if explanations:
                st.markdown("**🔍 Why this laptop?**")
                for explanation in explanations:
                    st.write(f"• {explanation}")

            # Purchase link placeholder
            if 'Brand' in laptop.index:
                st.markdown(f"""
                <div style="text-align: center; margin-top: 1rem;">
                    <a href="#" style="
                        background: linear-gradient(135deg, {COLORS["button_primary"]} 0%, {COLORS["accent"]} 100%);
                        color: white;
                        padding: 0.5rem 1.5rem;
                        border-radius: 8px;
                        text-decoration: none;
                        font-weight: 600;
                        display: inline-block;
                    ">🛒 View Details & Purchase</a>
                </div>
                """, unsafe_allow_html=True)

            st.markdown("---")

def render_laptop_details(laptop, idx):
    """Render detailed laptop specifications."""
    rec_col1, rec_col2, rec_col3 = st.columns(3)

    with rec_col1:
        if 'Brand' in laptop.index:
            st.write(f"🏷️ **Brand:** {laptop['Brand']}")
        if 'RAM' in laptop.index:
            st.write(f"💾 **RAM:** {laptop['RAM']} GB")
        if 'Storage' in laptop.index:
            st.write(f"💾 **Storage:** {laptop['Storage']} GB")

    with rec_col2:
        if 'Screen' in laptop.index:
            st.write(f"📺 **Screen:** {laptop['Screen']}\"")
        if 'CPU' in laptop.index:
            cpu_display = str(laptop['CPU'])[:30] + "..." if len(str(laptop['CPU'])) > 30 else str(laptop['CPU'])
            st.write(f"💻 **CPU:** {cpu_display}")
        if 'GPU' in laptop.index and pd.notna(laptop['GPU']):
            gpu_display = str(laptop['GPU'])[:30] + "..." if len(str(laptop['GPU'])) > 30 else str(laptop['GPU'])
            st.write(f"🎮 **GPU:** {gpu_display}")

    with rec_col3:
        if 'Final Price' in laptop.index:
            st.write(f"💰 **Price:** ${laptop['Final Price']:,.0f}")
        if 'Touch' in laptop.index:
            touch_status = "Yes" if laptop['Touch'] else "No"
            st.write(f"🔍 **Touchscreen:** {touch_status}")
        if 'Weight' in laptop.index:
            st.write(f"⚖️ **Weight:** {laptop['Weight']} kg")

def render_recommendation_summary(recommendations, user_preferences):
    """Render a summary of the recommendations."""
    st.markdown("### 📊 Recommendation Summary")

    if len(recommendations) > 0:
        # Price range of recommendations
        if 'Final Price' in recommendations.columns:
            min_price = recommendations['Final Price'].min()
            max_price = recommendations['Final Price'].max()
            avg_price = recommendations['Final Price'].mean()

            col_sum1, col_sum2, col_sum3 = st.columns(3)
            with col_sum1:
                st.metric("💰 Price Range", f"${min_price:,.0f} - ${max_price:,.0f}")
            with col_sum2:
                st.metric("📊 Average Price", f"${avg_price:,.0f}")
            with col_sum3:
                avg_score = recommendations['Recommendation_Score'].mean()
                st.metric("🎯 Avg Score", f"{avg_score:.3f}")

        # Brand distribution
        if 'Brand' in recommendations.columns:
            brand_counts = recommendations['Brand'].value_counts()
            st.markdown("**🏷️ Recommended Brands:**")
            for brand, count in brand_counts.items():
                st.write(f"• {brand}: {count} laptop(s)")

        # Show user preferences summary
        st.markdown("**👤 Your Preferences:**")
        st.write(f"• Usage: {user_preferences.get('usage_type', 'N/A')}")
        if 'budget_range' in user_preferences:
            budget_min, budget_max = user_preferences['budget_range']
            st.write(f"• Budget: ${budget_min:,.0f} - ${budget_max:,.0f}")
        st.write(f"• RAM: {user_preferences.get('preferred_ram', 'N/A')} GB")
        st.write(f"• Storage: {user_preferences.get('preferred_storage', 'N/A')} GB")
        st.write(f"• Brand: {user_preferences.get('preferred_brand', 'Any')}")

def render_comparison_table(recommendations):
    """Render a comparison table of recommended laptops."""
    if len(recommendations) > 1:
        st.markdown("### 📋 Quick Comparison")
        
        # Select key columns for comparison
        comparison_cols = []
        available_cols = ['Brand', 'RAM', 'Storage', 'Screen', 'Final Price', 'Recommendation_Score']
        
        for col in available_cols:
            if col in recommendations.columns:
                comparison_cols.append(col)
        
        if comparison_cols:
            comparison_df = recommendations[comparison_cols].copy()
            comparison_df.index = [f"Option {i+1}" for i in range(len(comparison_df))]
            
            # Format price column if present
            if 'Final Price' in comparison_df.columns:
                comparison_df['Final Price'] = comparison_df['Final Price'].apply(lambda x: f"${x:,.0f}")
            
            # Format score column
            if 'Recommendation_Score' in comparison_df.columns:
                comparison_df['Recommendation_Score'] = comparison_df['Recommendation_Score'].apply(lambda x: f"{x:.3f}")
            
            st.dataframe(comparison_df, use_container_width=True)
