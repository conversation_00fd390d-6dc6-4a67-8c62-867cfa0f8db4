# ==========================
# Data Processing and Cleaning Functions
# ==========================

import pandas as pd
import numpy as np
import streamlit as st
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, Normalizer
from sklearn.preprocessing import LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer, KNNImputer
from scipy.stats import zscore

# Handle IterativeImputer import for different sklearn versions
try:
    from sklearn.impute import IterativeImputer
except ImportError:
    IterativeImputer = None

class DataProcessor:
    """Class to handle all data preprocessing operations."""
    
    def __init__(self):
        self.operations_log = []
        self.scalers = {}
        self.encoders = {}
    
    def initialize_preprocessing_pipeline(self, data):
        """Initialize the preprocessing pipeline in session state."""
        if 'preprocessing_pipeline' not in st.session_state:
            st.session_state.preprocessing_pipeline = {
                'original_data': data.copy(),
                'current_data': data.copy(),
                'missing_values_handled': False,
                'encoding_applied': False,
                'outliers_handled': False,
                'scaling_applied': False,
                'operations_log': []
            }
    
    def handle_missing_values(self, data, method, columns=None, **kwargs):
        """
        Handle missing values in the dataset.
        
        Args:
            data (pd.DataFrame): Input data
            method (str): Method to handle missing values
            columns (list): Columns to process (None for all)
            **kwargs: Additional parameters for specific methods
            
        Returns:
            pd.DataFrame: Processed data
            str: Operation description
        """
        data_copy = data.copy()
        
        if columns is None:
            columns = data_copy.columns[data_copy.isnull().any()].tolist()
        
        if not columns:
            return data_copy, "No missing values found"
        
        operation_desc = ""
        
        if method == "Drop Rows":
            initial_rows = len(data_copy)
            data_copy = data_copy.dropna(subset=columns)
            dropped_rows = initial_rows - len(data_copy)
            operation_desc = f"Dropped {dropped_rows} rows with missing values in {', '.join(columns)}"
        
        elif method == "Drop Columns":
            data_copy = data_copy.drop(columns=columns)
            operation_desc = f"Dropped columns: {', '.join(columns)}"
        
        elif method == "Mean Imputation":
            numerical_cols = [col for col in columns if data_copy[col].dtype in ['int64', 'float64']]
            if numerical_cols:
                imputer = SimpleImputer(strategy='mean')
                data_copy[numerical_cols] = imputer.fit_transform(data_copy[numerical_cols])
                operation_desc = f"Mean imputation applied to: {', '.join(numerical_cols)}"
        
        elif method == "Median Imputation":
            numerical_cols = [col for col in columns if data_copy[col].dtype in ['int64', 'float64']]
            if numerical_cols:
                imputer = SimpleImputer(strategy='median')
                data_copy[numerical_cols] = imputer.fit_transform(data_copy[numerical_cols])
                operation_desc = f"Median imputation applied to: {', '.join(numerical_cols)}"
        
        elif method == "Mode Imputation":
            categorical_cols = [col for col in columns if data_copy[col].dtype == 'object']
            if categorical_cols:
                imputer = SimpleImputer(strategy='most_frequent')
                data_copy[categorical_cols] = imputer.fit_transform(data_copy[categorical_cols])
                operation_desc = f"Mode imputation applied to: {', '.join(categorical_cols)}"
        
        elif method == "Forward Fill":
            data_copy[columns] = data_copy[columns].fillna(method='ffill')
            operation_desc = f"Forward fill applied to: {', '.join(columns)}"
        
        elif method == "Backward Fill":
            data_copy[columns] = data_copy[columns].fillna(method='bfill')
            operation_desc = f"Backward fill applied to: {', '.join(columns)}"
        
        elif method == "KNN Imputation":
            numerical_cols = [col for col in columns if data_copy[col].dtype in ['int64', 'float64']]
            if numerical_cols:
                n_neighbors = kwargs.get('n_neighbors', 5)
                imputer = KNNImputer(n_neighbors=n_neighbors)
                data_copy[numerical_cols] = imputer.fit_transform(data_copy[numerical_cols])
                operation_desc = f"KNN imputation (k={n_neighbors}) applied to: {', '.join(numerical_cols)}"
        
        elif method == "Iterative Imputation" and IterativeImputer is not None:
            numerical_cols = [col for col in columns if data_copy[col].dtype in ['int64', 'float64']]
            if numerical_cols:
                imputer = IterativeImputer(random_state=42)
                data_copy[numerical_cols] = imputer.fit_transform(data_copy[numerical_cols])
                operation_desc = f"Iterative imputation applied to: {', '.join(numerical_cols)}"
        
        return data_copy, operation_desc
    
    def encode_categorical_features(self, data, method, columns=None, **kwargs):
        """
        Encode categorical features.
        
        Args:
            data (pd.DataFrame): Input data
            method (str): Encoding method
            columns (list): Columns to encode
            **kwargs: Additional parameters
            
        Returns:
            pd.DataFrame: Encoded data
            str: Operation description
        """
        data_copy = data.copy()
        
        if columns is None:
            columns = data_copy.select_dtypes(include=['object']).columns.tolist()
        
        if not columns:
            return data_copy, "No categorical columns found"
        
        operation_desc = ""
        
        if method == "Label Encoding":
            for col in columns:
                le = LabelEncoder()
                data_copy[col] = le.fit_transform(data_copy[col].astype(str))
                self.encoders[col] = le
            operation_desc = f"Label encoding applied to: {', '.join(columns)}"
        
        elif method == "One-Hot Encoding":
            # Get dummies for specified columns
            data_encoded = pd.get_dummies(data_copy, columns=columns, prefix=columns)
            operation_desc = f"One-hot encoding applied to: {', '.join(columns)}"
            return data_encoded, operation_desc
        
        return data_copy, operation_desc
    
    def detect_outliers(self, data, column, method='IQR'):
        """
        Detect outliers in a numerical column.
        
        Args:
            data (pd.DataFrame): Input data
            column (str): Column to analyze
            method (str): Detection method ('IQR' or 'Z-Score')
            
        Returns:
            dict: Outlier information
        """
        if column not in data.columns or data[column].dtype not in ['int64', 'float64']:
            return None
        
        col_data = data[column].dropna()
        
        if method == 'IQR':
            Q1 = col_data.quantile(0.25)
            Q3 = col_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers_mask = (col_data < lower_bound) | (col_data > upper_bound)
        
        elif method == 'Z-Score':
            z_scores = np.abs(zscore(col_data))
            threshold = 3
            outliers_mask = z_scores > threshold
        
        outlier_count = outliers_mask.sum()
        outlier_percentage = (outlier_count / len(col_data)) * 100
        
        return {
            'column': column,
            'method': method,
            'outlier_count': outlier_count,
            'outlier_percentage': outlier_percentage,
            'outliers_mask': outliers_mask,
            'outlier_values': col_data[outliers_mask].tolist()
        }
    
    def handle_outliers(self, data, column, method, **kwargs):
        """
        Handle outliers in the dataset.
        
        Args:
            data (pd.DataFrame): Input data
            column (str): Column to process
            method (str): Handling method
            **kwargs: Additional parameters
            
        Returns:
            pd.DataFrame: Processed data
            str: Operation description
        """
        data_copy = data.copy()
        operation_desc = ""
        
        if method == "Remove Outliers":
            outlier_info = kwargs.get('outlier_info')
            if outlier_info:
                outliers_mask = outlier_info['outliers_mask']
                # Remove rows with outliers
                full_mask = data_copy[column].isin(data_copy[column][~outliers_mask])
                data_copy = data_copy[full_mask]
                operation_desc = f"Removed {outlier_info['outlier_count']} outliers from {column}"
        
        elif method == "Cap Outliers (Winsorization)":
            lower_percentile = kwargs.get('lower_percentile', 0.05)
            upper_percentile = kwargs.get('upper_percentile', 0.95)
            lower_cap = data_copy[column].quantile(lower_percentile)
            upper_cap = data_copy[column].quantile(upper_percentile)
            data_copy[column] = data_copy[column].clip(lower=lower_cap, upper=upper_cap)
            operation_desc = f"Winsorized {column} (capped at {lower_percentile:.2f}-{upper_percentile:.2f} percentiles)"
        
        elif method == "Transform with Log":
            if (data_copy[column] > 0).all():
                data_copy[column] = np.log(data_copy[column])
                operation_desc = f"Applied log transformation to {column}"
            else:
                data_copy[column] = np.log1p(data_copy[column])
                operation_desc = f"Applied log1p transformation to {column}"
        
        return data_copy, operation_desc
    
    def scale_features(self, data, method, columns=None):
        """
        Scale numerical features.
        
        Args:
            data (pd.DataFrame): Input data
            method (str): Scaling method
            columns (list): Columns to scale
            
        Returns:
            pd.DataFrame: Scaled data
            str: Operation description
        """
        data_copy = data.copy()
        
        if columns is None:
            columns = data_copy.select_dtypes(include=[np.number]).columns.tolist()
        
        if not columns:
            return data_copy, "No numerical columns found for scaling"
        
        operation_desc = ""
        
        if method == "Standard Scaler":
            scaler = StandardScaler()
            data_copy[columns] = scaler.fit_transform(data_copy[columns])
            self.scalers['standard'] = scaler
            operation_desc = f"Standard Scaler applied to {', '.join(columns)}"
        
        elif method == "MinMax Scaler":
            scaler = MinMaxScaler()
            data_copy[columns] = scaler.fit_transform(data_copy[columns])
            self.scalers['minmax'] = scaler
            operation_desc = f"MinMax Scaler applied to {', '.join(columns)}"
        
        elif method == "Robust Scaler":
            scaler = RobustScaler()
            data_copy[columns] = scaler.fit_transform(data_copy[columns])
            self.scalers['robust'] = scaler
            operation_desc = f"Robust Scaler applied to {', '.join(columns)}"
        
        elif method == "Normalizer":
            scaler = Normalizer()
            data_copy[columns] = scaler.fit_transform(data_copy[columns])
            self.scalers['normalizer'] = scaler
            operation_desc = f"Normalizer applied to {', '.join(columns)}"
        
        return data_copy, operation_desc
    
    def remove_duplicates(self, data, subset=None, keep='first'):
        """
        Remove duplicate rows from the dataset.
        
        Args:
            data (pd.DataFrame): Input data
            subset (list): Columns to consider for duplicates
            keep (str): Which duplicates to keep
            
        Returns:
            pd.DataFrame: Data without duplicates
            str: Operation description
        """
        initial_rows = len(data)
        data_copy = data.drop_duplicates(subset=subset, keep=keep)
        removed_rows = initial_rows - len(data_copy)
        
        if subset:
            operation_desc = f"Removed {removed_rows} duplicate rows based on columns: {', '.join(subset)}"
        else:
            operation_desc = f"Removed {removed_rows} duplicate rows"
        
        return data_copy, operation_desc
    
    def get_data_summary(self, data):
        """
        Get a comprehensive summary of the dataset.
        
        Args:
            data (pd.DataFrame): Input data
            
        Returns:
            dict: Data summary
        """
        summary = {
            'shape': data.shape,
            'memory_usage': data.memory_usage(deep=True).sum() / 1024,  # KB
            'missing_values': data.isnull().sum().sum(),
            'duplicate_rows': data.duplicated().sum(),
            'numerical_columns': len(data.select_dtypes(include=[np.number]).columns),
            'categorical_columns': len(data.select_dtypes(include=['object']).columns),
            'data_types': data.dtypes.to_dict()
        }
        
        # Column-wise missing values
        summary['missing_by_column'] = data.isnull().sum().to_dict()
        
        # Basic statistics for numerical columns
        numerical_cols = data.select_dtypes(include=[np.number]).columns
        if len(numerical_cols) > 0:
            summary['numerical_stats'] = data[numerical_cols].describe().to_dict()
        
        return summary
