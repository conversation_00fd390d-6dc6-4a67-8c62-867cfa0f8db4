# ==========================
# Welcome Tab Component
# ==========================

import streamlit as st
from config import COLORS
from data_loader import get_data_info

def render_welcome_tab(data):
    """Render the welcome tab content."""
    
    # Hero Section with Modern Calm Theme
    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, {COLORS["heading"]} 0%, {COLORS["button_primary"]} 100%);
        padding: 3rem 2rem;
        border-radius: 25px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 15px 40px rgba(50, 18, 122, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    ">
        <h1 style="font-size: 2.8rem; margin-bottom: 1rem; font-weight: 700; letter-spacing: -0.02em;">👋 Welcome to Your Tech Journey!</h1>
        <p style="font-size: 1.4rem; margin-bottom: 1.5rem; opacity: 0.95; line-height: 1.6;">Find your perfect laptop with our intelligent AI-powered recommendation system</p>
    </div>
    """, unsafe_allow_html=True)

    # Dataset Overview Section
    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, {COLORS["card_background"]} 0%, {COLORS["secondary_background"]} 100%);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        border-left: 5px solid {COLORS["button_primary"]};
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(50, 18, 122, 0.1);
    ">
        <h2 style="color: {COLORS["heading"]}; margin-bottom: 1.5rem; display: flex; align-items: center; font-weight: 700;">
            <span style="margin-right: 0.5rem;">📊</span> Dataset Overview
        </h2>
        <p style="color: {COLORS["heading"]}; opacity: 0.8; font-size: 1.1rem; margin: 0;">Comprehensive laptop data analysis for informed decision-making</p>
    </div>
    """, unsafe_allow_html=True)

    # Dataset Statistics
    if not data.empty:
        data_info = get_data_info(data)
        render_dataset_metrics(data_info)

    # Features Section
    render_features_section()

    # Call to Action
    render_call_to_action()

def render_dataset_metrics(data_info):
    """Render dataset metrics cards."""
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        total_laptops = data_info.get("total_rows", 0)
        st.markdown(f"""
        <div class="metric-card" style="
            background: linear-gradient(135deg, {COLORS["heading"]} 0%, {COLORS["button_primary"]} 100%);
            color: white;
            transform: scale(1);
            transition: all 0.3s ease;
        ">
            <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💻</div>
            <div style="font-size: 1.8rem; font-weight: bold;">{total_laptops:,}</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">Total Laptops</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        unique_brands = data_info.get("unique_brands", 0)
        st.markdown(f"""
        <div class="metric-card" style="
            background: linear-gradient(135deg, {COLORS["accent"]} 0%, {COLORS["heading"]} 100%);
            color: white;
        ">
            <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">🏷️</div>
            <div style="font-size: 1.8rem; font-weight: bold;">{unique_brands}</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">Unique Brands</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        min_price = data_info.get("min_price", 0)
        max_price = data_info.get("max_price", 0)
        if min_price and max_price:
            price_range = f"${min_price:,.0f} - ${max_price:,.0f}"
        else:
            price_range = "N/A"
        st.markdown(f"""
        <div class="metric-card" style="
            background: linear-gradient(135deg, {COLORS["button_primary"]} 0%, {COLORS["secondary_background"]} 100%);
            color: {COLORS["heading"]};
        ">
            <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💰</div>
            <div style="font-size: 1.2rem; font-weight: bold;">{price_range}</div>
            <div style="font-size: 0.9rem; opacity: 0.8;">Price Range</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        avg_ram = data_info.get("avg_ram", 0)
        ram_info = f"{avg_ram:.0f} GB" if avg_ram else "N/A"
        st.markdown(f"""
        <div class="metric-card" style="
            background: linear-gradient(135deg, {COLORS["card_background"]} 0%, {COLORS["accent"]} 100%);
            color: white;
        ">
            <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">🧠</div>
            <div style="font-size: 1.8rem; font-weight: bold;">{ram_info}</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">Avg RAM</div>
        </div>
        """, unsafe_allow_html=True)

    with col5:
        avg_storage = data_info.get("avg_storage", 0)
        storage_info = f"{avg_storage:.0f} GB" if avg_storage else "N/A"
        st.markdown(f"""
        <div class="metric-card" style="
            background: linear-gradient(135deg, {COLORS["secondary_background"]} 0%, {COLORS["button_primary"]} 100%);
            color: {COLORS["heading"]};
        ">
            <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💾</div>
            <div style="font-size: 1.8rem; font-weight: bold;">{storage_info}</div>
            <div style="font-size: 0.9rem; opacity: 0.8;">Avg Storage</div>
        </div>
        """, unsafe_allow_html=True)

def render_features_section():
    """Render the features section."""
    st.markdown(f"""
    <div style="margin: 3rem 0 2rem 0;">
        <h3 style="color: {COLORS["heading"]}; text-align: center; margin-bottom: 2rem; font-weight: 700; font-size: 2rem;">🚀 Platform Features</h3>
        <p style="color: {COLORS["heading"]}; text-align: center; opacity: 0.7; font-size: 1.1rem; margin-bottom: 2rem;">Powerful tools designed for the modern tech community</p>
    </div>
    """, unsafe_allow_html=True)

    # Feature cards with enhanced design
    col1, col2 = st.columns(2)

    with col1:
        st.markdown(f"""
        <div class="modern-card" style="
            background: linear-gradient(135deg, {COLORS["button_primary"]} 0%, {COLORS["heading"]} 100%);
            color: white;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">📊</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Data Visualization</h4>
            </div>
            <p style="margin: 0; opacity: 0.95; line-height: 1.6; font-size: 1rem;">Explore comprehensive charts and graphs to understand laptop market trends, brand distributions, and feature correlations.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(f"""
        <div class="modern-card" style="
            background: linear-gradient(135deg, {COLORS["secondary_background"]} 0%, {COLORS["button_primary"]} 100%);
            color: {COLORS["heading"]};
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🤖</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Model Evaluation</h4>
            </div>
            <p style="margin: 0; opacity: 0.9; line-height: 1.6; font-size: 1rem;">Evaluate machine learning models with detailed performance metrics including accuracy, precision, recall, and F1-score.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="modern-card" style="
            background: linear-gradient(135deg, {COLORS["accent"]} 0%, {COLORS["heading"]} 100%);
            color: white;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🧹</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Data Cleaning</h4>
            </div>
            <p style="margin: 0; opacity: 0.95; line-height: 1.6; font-size: 1rem;">Clean and preprocess data with advanced tools for handling missing values, outliers, duplicates, and feature engineering.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(f"""
        <div class="modern-card" style="
            background: linear-gradient(135deg, {COLORS["card_background"]} 0%, {COLORS["accent"]} 100%);
            color: {COLORS["heading"]};
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">💻</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Laptop Recommendation</h4>
            </div>
            <p style="margin: 0; opacity: 0.9; line-height: 1.6; font-size: 1rem;">Get personalized laptop recommendations based on your budget, usage requirements, and preferred specifications.</p>
        </div>
        """, unsafe_allow_html=True)

def render_call_to_action():
    """Render the call to action section."""
    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, {COLORS["heading"]} 0%, {COLORS["button_primary"]} 100%);
        padding: 2.5rem;
        border-radius: 20px;
        text-align: center;
        margin: 2rem 0;
        color: white;
        box-shadow: 0 12px 40px rgba(50, 18, 122, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    ">
        <h3 style="margin-bottom: 1rem; font-size: 1.8rem; font-weight: 700;">🎆 Ready to Find Your Perfect Laptop?</h3>
        <p style="margin-bottom: 2rem; opacity: 0.95; font-size: 1.2rem; line-height: 1.5;">Navigate through our tabs to explore data insights or jump directly to our recommendation engine!</p>
        <div style="display: flex; justify-content: center; gap: 1.5rem; flex-wrap: wrap;">
            <div style="background: rgba(227, 204, 220, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">📊 Analyze Data</div>
            <div style="background: rgba(205, 224, 225, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">🧹 Clean Data</div>
            <div style="background: rgba(179, 68, 108, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">🤖 Evaluate Models</div>
            <div style="background: rgba(0, 130, 146, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">💻 Get Recommendations</div>
        </div>
    </div>
    """, unsafe_allow_html=True)
