# ==========================
# Data Cleaning Tab Component
# ==========================

import streamlit as st
import pandas as pd
import numpy as np
from config import COLORS
from ui.styles import create_section_header, create_info_card
from core.data_processor import DataProcessor

def render_cleaning_tab(data):
    """Render the data cleaning tab content."""
    
    # Enhanced header
    st.markdown(create_section_header(
        "🧹 Data Cleaning Insights",
        "Interactive data preprocessing pipeline with step-by-step workflow"
    ), unsafe_allow_html=True)

    # Initialize data processor
    processor = DataProcessor()
    processor.initialize_preprocessing_pipeline(data)

    # Create preprocessing tabs
    prep_tab1, prep_tab2, prep_tab3, prep_tab4, prep_tab5 = st.tabs([
        "🔍 Handle Missing Values",
        "🔠 Encoding", 
        "🎯 Handle Outliers",
        "⚖️ Scaling",
        "📦 Final Processed Data"
    ])

    with prep_tab1:
        render_missing_values_tab(processor)
    
    with prep_tab2:
        render_encoding_tab(processor)
    
    with prep_tab3:
        render_outliers_tab(processor)
    
    with prep_tab4:
        render_scaling_tab(processor)
    
    with prep_tab5:
        render_final_data_tab(processor)

def render_missing_values_tab(processor):
    """Render the missing values handling tab."""
    st.markdown(create_info_card(
        "🔍 Handle Missing Values",
        "Step 1: Identify and handle missing values in your dataset"
    ), unsafe_allow_html=True)

    current_data = st.session_state.preprocessing_pipeline['current_data'].copy()
    
    # Show first 5 rows
    st.markdown("**📅 Current Data (First 5 Rows):**")
    st.dataframe(current_data.head(), use_container_width=True)

    # Missing values analysis
    missing_info = current_data.isnull().sum()
    missing_cols = missing_info[missing_info > 0]

    if len(missing_cols) > 0:
        st.markdown("**🔍 Missing Values Analysis:**")
        
        col1, col2 = st.columns(2)
        with col1:
            st.dataframe(missing_cols.to_frame('Missing Count'), use_container_width=True)
        
        with col2:
            missing_pct = (missing_cols / len(current_data) * 100).round(2)
            st.dataframe(missing_pct.to_frame('Missing %'), use_container_width=True)

        # Missing values handling options
        st.markdown("**🛠️ Choose Handling Method:**")
        
        col1, col2 = st.columns(2)
        with col1:
            handling_method = st.selectbox(
                "Select method:",
                ["Drop Rows", "Drop Columns", "Mean Imputation", "Median Imputation", 
                 "Mode Imputation", "Forward Fill", "Backward Fill", "KNN Imputation"],
                key="missing_method"
            )
        
        with col2:
            selected_columns = st.multiselect(
                "Select columns:",
                missing_cols.index.tolist(),
                default=missing_cols.index.tolist(),
                key="missing_columns"
            )

        # Additional parameters for specific methods
        if handling_method == "KNN Imputation":
            n_neighbors = st.slider("Number of neighbors:", 1, 10, 5, key="knn_neighbors")
        else:
            n_neighbors = 5

        # Apply missing values handling
        if st.button(f"🚀 Apply {handling_method}", type="primary", key="apply_missing"):
            if selected_columns:
                with st.spinner(f"🔄 Applying {handling_method}..."):
                    try:
                        processed_data, operation_desc = processor.handle_missing_values(
                            current_data, handling_method, selected_columns, n_neighbors=n_neighbors
                        )
                        
                        # Update session state
                        st.session_state.preprocessing_pipeline['current_data'] = processed_data
                        st.session_state.preprocessing_pipeline['operations_log'].append(operation_desc)
                        st.session_state.preprocessing_pipeline['missing_values_handled'] = True
                        
                        st.balloons()
                        st.success(f"✅ {handling_method} applied successfully! You can now proceed to the Encoding tab.")
                        st.rerun()
                        
                    except Exception as e:
                        st.error(f"❌ Error applying {handling_method}: {str(e)}")
            else:
                st.warning("⚠️ Please select at least one column.")

        # Option to skip missing values handling
        if st.button("⏭️ Skip Missing Values Handling", type="secondary", key="skip_missing"):
            st.session_state.preprocessing_pipeline['missing_values_handled'] = True
            st.info("📍 Missing values handling skipped. You can now proceed to the Encoding tab.")
            st.rerun()

    else:
        st.session_state.preprocessing_pipeline['missing_values_handled'] = True
        st.success("✅ No missing values found! You can proceed to the Encoding tab.")

def render_encoding_tab(processor):
    """Render the encoding tab."""
    st.markdown(create_info_card(
        "🔠 Encoding",
        "Step 2: Encode categorical variables for machine learning compatibility"
    ), unsafe_allow_html=True)

    # Check if missing values handling is completed
    if not st.session_state.preprocessing_pipeline['missing_values_handled']:
        st.warning("⚠️ Please complete missing values handling first before proceeding to encoding.")
        st.info("📍 Go to the 'Handle Missing Values' tab to complete step 1.")
    else:
        current_data = st.session_state.preprocessing_pipeline['current_data'].copy()
        
        # Show first 5 rows
        st.markdown("**📅 Current Data (First 5 Rows):**")
        st.dataframe(current_data.head(), use_container_width=True)

        # Get categorical columns
        categorical_cols = current_data.select_dtypes(include=['object']).columns.tolist()

        if categorical_cols:
            st.markdown("**🔠 Categorical Columns Found:**")
            st.write(f"Found {len(categorical_cols)} categorical columns: {', '.join(categorical_cols)}")

            # Show sample values for each categorical column
            for col in categorical_cols:
                unique_values = current_data[col].nunique()
                sample_values = current_data[col].dropna().unique()[:5]
                st.write(f"**{col}**: {unique_values} unique values (Sample: {', '.join(map(str, sample_values))})")

            # Encoding options
            col1, col2 = st.columns(2)

            with col1:
                encoding_method = st.selectbox(
                    "Choose encoding method:",
                    ["Label Encoding", "One-Hot Encoding"],
                    key="encoding_method"
                )

            with col2:
                selected_cols_encoding = st.multiselect(
                    "Choose columns to encode:",
                    categorical_cols,
                    default=categorical_cols,
                    key="encoding_columns"
                )

            # Show encoding method information
            encoding_info = {
                "Label Encoding": "Converts categories to numerical labels (0, 1, 2, ...). Best for ordinal data or when you have many categories.",
                "One-Hot Encoding": "Creates binary columns for each category. Best for nominal data with few categories."
            }

            st.info(f"📍 **{encoding_method}**: {encoding_info[encoding_method]}")

            # Apply encoding
            if st.button(f"🚀 Apply {encoding_method}", type="primary", key="apply_encoding"):
                if selected_cols_encoding:
                    with st.spinner(f"🔄 Applying {encoding_method}..."):
                        try:
                            processed_data, operation_desc = processor.encode_categorical_features(
                                current_data, encoding_method, selected_cols_encoding
                            )
                            
                            # Update session state
                            st.session_state.preprocessing_pipeline['current_data'] = processed_data
                            st.session_state.preprocessing_pipeline['operations_log'].append(operation_desc)
                            st.session_state.preprocessing_pipeline['encoding_applied'] = True
                            
                            st.balloons()
                            st.success(f"✅ {encoding_method} applied successfully! You can now proceed to the Outliers tab.")
                            st.rerun()
                            
                        except Exception as e:
                            st.error(f"❌ Error applying {encoding_method}: {str(e)}")
                else:
                    st.warning("⚠️ Please select at least one column to encode.")

            # Option to skip encoding
            if st.button("⏭️ Skip Encoding", type="secondary", key="skip_encoding"):
                st.session_state.preprocessing_pipeline['encoding_applied'] = True
                st.info("📍 Encoding skipped. You can now proceed to the Outliers tab.")
                st.rerun()

        else:
            st.session_state.preprocessing_pipeline['encoding_applied'] = True
            st.success("✅ No categorical columns found! You can proceed to the Outliers tab.")

def render_outliers_tab(processor):
    """Render the outliers handling tab."""
    st.markdown(create_info_card(
        "🎯 Handle Outliers",
        "Step 3: Detect and handle outliers in numerical features"
    ), unsafe_allow_html=True)

    # Check if encoding is completed
    if not st.session_state.preprocessing_pipeline['encoding_applied']:
        st.warning("⚠️ Please complete encoding first before proceeding to outlier handling.")
        st.info("📍 Go to the 'Encoding' tab to complete step 2.")
    else:
        current_data = st.session_state.preprocessing_pipeline['current_data'].copy()
        
        # Show first 5 rows
        st.markdown("**📅 Current Data (First 5 Rows):**")
        st.dataframe(current_data.head(), use_container_width=True)

        # Get numerical columns
        numerical_cols = current_data.select_dtypes(include=[np.number]).columns.tolist()

        if numerical_cols:
            st.markdown("**📊 Numerical Columns Available for Outlier Detection:**")
            st.write(f"Found {len(numerical_cols)} numerical columns: {', '.join(numerical_cols)}")

            # Outlier detection
            col1, col2 = st.columns(2)

            with col1:
                selected_column = st.selectbox(
                    "Select column for outlier detection:",
                    numerical_cols,
                    key="outlier_column"
                )

            with col2:
                detection_method = st.selectbox(
                    "Choose detection method:",
                    ["IQR", "Z-Score"],
                    key="detection_method"
                )

            # Detect outliers button
            if st.button("🔍 Detect Outliers", type="secondary", key="detect_outliers"):
                with st.spinner("🔄 Detecting outliers..."):
                    outlier_info = processor.detect_outliers(current_data, selected_column, detection_method)
                    
                    if outlier_info:
                        st.session_state['outlier_info'] = outlier_info
                        
                        # Display outlier information
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("🎯 Outliers Found", outlier_info['outlier_count'])
                        with col2:
                            st.metric("📊 Percentage", f"{outlier_info['outlier_percentage']:.2f}%")
                        with col3:
                            st.metric("🔍 Method", outlier_info['method'])

                        if outlier_info['outlier_count'] > 0:
                            st.markdown("**🎯 Outlier Values (First 10):**")
                            outlier_values = outlier_info['outlier_values'][:10]
                            st.write(", ".join(map(str, outlier_values)))

            # Handle outliers if detected
            if 'outlier_info' in st.session_state and st.session_state['outlier_info']:
                outlier_info = st.session_state['outlier_info']
                
                if outlier_info['outlier_count'] > 0:
                    st.markdown("**🛠️ Choose Outlier Handling Method:**")
                    
                    handling_method = st.selectbox(
                        "Select method:",
                        ["Remove Outliers", "Cap Outliers (Winsorization)", "Transform with Log"],
                        key="outlier_handling_method"
                    )

                    # Additional parameters for winsorization
                    if handling_method == "Cap Outliers (Winsorization)":
                        col_win1, col_win2 = st.columns(2)
                        with col_win1:
                            lower_percentile = st.slider(
                                "Lower percentile to cap:",
                                min_value=0.01,
                                max_value=0.10,
                                value=0.05,
                                step=0.01,
                                key="lower_percentile"
                            )
                        with col_win2:
                            upper_percentile = st.slider(
                                "Upper percentile to cap:",
                                min_value=0.90,
                                max_value=0.99,
                                value=0.95,
                                step=0.01,
                                key="upper_percentile"
                            )

                    # Apply outlier handling
                    if st.button(f"🚀 Apply {handling_method}", type="primary", key="apply_outlier_handling"):
                        with st.spinner(f"🔄 Applying {handling_method}..."):
                            try:
                                kwargs = {}
                                if handling_method == "Remove Outliers":
                                    kwargs['outlier_info'] = outlier_info
                                elif handling_method == "Cap Outliers (Winsorization)":
                                    kwargs['lower_percentile'] = lower_percentile
                                    kwargs['upper_percentile'] = upper_percentile

                                processed_data, operation_desc = processor.handle_outliers(
                                    current_data, outlier_info['column'], handling_method, **kwargs
                                )
                                
                                # Update session state
                                st.session_state.preprocessing_pipeline['current_data'] = processed_data
                                st.session_state.preprocessing_pipeline['operations_log'].append(operation_desc)
                                st.session_state.preprocessing_pipeline['outliers_handled'] = True
                                
                                # Clear outlier info
                                st.session_state['outlier_info'] = None
                                
                                st.balloons()
                                st.success(f"✅ {handling_method} applied successfully! You can now proceed to the Scaling tab.")
                                st.rerun()
                                
                            except Exception as e:
                                st.error(f"❌ Error applying {handling_method}: {str(e)}")

            # Option to skip outlier handling
            if st.button("⏭️ Skip Outlier Handling", type="secondary", key="skip_outliers"):
                st.session_state.preprocessing_pipeline['outliers_handled'] = True
                st.info("📍 Outlier handling skipped. You can now proceed to the Scaling tab.")
                st.rerun()

        else:
            st.session_state.preprocessing_pipeline['outliers_handled'] = True
            st.success("✅ No numerical columns found! You can proceed to the Scaling tab.")

def render_scaling_tab(processor):
    """Render the scaling tab."""
    st.markdown(create_info_card(
        "⚖️ Scaling",
        "Step 4: Scale numerical features for optimal machine learning performance"
    ), unsafe_allow_html=True)

    # Check if outlier handling is completed
    if not st.session_state.preprocessing_pipeline['outliers_handled']:
        st.warning("⚠️ Please complete outlier handling first before proceeding to scaling.")
        st.info("📍 Go to the 'Handle Outliers' tab to complete step 3.")
    else:
        current_data = st.session_state.preprocessing_pipeline['current_data'].copy()

        # Show first 5 rows
        st.markdown("**📅 Current Data (First 5 Rows):**")
        st.dataframe(current_data.head(), use_container_width=True)

        # Get numerical columns
        numerical_cols = current_data.select_dtypes(include=[np.number]).columns.tolist()

        if numerical_cols:
            st.markdown("**📊 Numerical Columns Available for Scaling:**")
            st.write(f"Found {len(numerical_cols)} numerical columns: {', '.join(numerical_cols)}")

            # Show data statistics before scaling
            st.markdown("**📊 Data Statistics (Before Scaling):**")
            stats_df = current_data[numerical_cols].describe()
            st.dataframe(stats_df, use_container_width=True)

            # Scaling options
            col1, col2 = st.columns(2)

            with col1:
                scaling_method = st.selectbox(
                    "Choose scaling method:",
                    ["Standard Scaler", "MinMax Scaler", "Robust Scaler", "Normalizer"],
                    key="scaling_method"
                )

            with col2:
                selected_cols_scaling = st.multiselect(
                    "Choose columns to scale:",
                    numerical_cols,
                    default=numerical_cols,
                    key="scaling_columns"
                )

            # Show scaling method information
            scaling_info = {
                "Standard Scaler": "Standardizes features by removing mean and scaling to unit variance (z-score normalization). Best for normally distributed data.",
                "MinMax Scaler": "Scales features to a fixed range [0,1]. Preserves relationships but sensitive to outliers.",
                "Robust Scaler": "Uses median and IQR for scaling. Robust to outliers but may not preserve exact relationships.",
                "Normalizer": "Scales individual samples to have unit norm. Good for text analysis and when direction matters more than magnitude."
            }

            st.info(f"📍 **{scaling_method}**: {scaling_info[scaling_method]}")

            # Apply scaling
            if st.button(f"🚀 Apply {scaling_method}", type="primary", key="apply_scaling"):
                if selected_cols_scaling:
                    with st.spinner(f"🔄 Applying {scaling_method}..."):
                        try:
                            processed_data, operation_desc = processor.scale_features(
                                current_data, scaling_method, selected_cols_scaling
                            )
                            
                            # Update session state
                            st.session_state.preprocessing_pipeline['current_data'] = processed_data
                            st.session_state.preprocessing_pipeline['operations_log'].append(operation_desc)
                            st.session_state.preprocessing_pipeline['scaling_applied'] = True
                            
                            st.balloons()
                            st.success(f"✅ {scaling_method} applied successfully! You can now view the final processed data.")

                            # Show statistics after scaling
                            st.markdown("**📊 Data Statistics (After Scaling):**")
                            stats_after = processed_data[selected_cols_scaling].describe()
                            st.dataframe(stats_after, use_container_width=True)

                            st.rerun()
                            
                        except Exception as e:
                            st.error(f"❌ Error applying {scaling_method}: {str(e)}")
                else:
                    st.warning("⚠️ Please select at least one column to scale.")

            # Option to skip scaling
            if st.button("⏭️ Skip Scaling", type="secondary", key="skip_scaling"):
                st.session_state.preprocessing_pipeline['scaling_applied'] = True
                st.info("📍 Scaling skipped. You can now view the final processed data.")
                st.rerun()

        else:
            st.session_state.preprocessing_pipeline['scaling_applied'] = True
            st.success("✅ No numerical columns found! You can view the final processed data.")

def render_final_data_tab(processor):
    """Render the final processed data tab."""
    st.markdown(create_info_card(
        "📦 Final Processed Data",
        "Step 5: Review and download your fully processed dataset"
    ), unsafe_allow_html=True)

    # Check if all preprocessing steps are completed
    pipeline = st.session_state.preprocessing_pipeline
    all_steps_completed = (
        pipeline['missing_values_handled'] and
        pipeline['encoding_applied'] and
        pipeline['outliers_handled'] and
        pipeline['scaling_applied']
    )

    if not all_steps_completed:
        st.warning("⚠️ Please complete all preprocessing steps before viewing the final data.")

        # Show progress
        st.markdown("**📊 Preprocessing Progress:**")
        progress_items = [
            ("🔍 Handle Missing Values", pipeline['missing_values_handled']),
            ("🔠 Encoding", pipeline['encoding_applied']),
            ("🎯 Handle Outliers", pipeline['outliers_handled']),
            ("⚖️ Scaling", pipeline['scaling_applied'])
        ]

        for step_name, completed in progress_items:
            if completed:
                st.success(f"✅ {step_name} - Completed")
            else:
                st.error(f"❌ {step_name} - Pending")

    else:
        # All steps completed - show final data
        final_data = pipeline['current_data'].copy()
        original_data = pipeline['original_data'].copy()

        st.success("🎉 All preprocessing steps completed successfully!")

        # Data transformation summary
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {COLORS["secondary_background"]} 0%, {COLORS["card_background"]} 100%);
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            text-align: center;
        ">
            <h4 style="color: {COLORS["heading"]}; margin-bottom: 1rem; font-weight: 700;">📊 Data Transformation Summary</h4>
        </div>
        """, unsafe_allow_html=True)

        # Before vs After comparison
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**📅 Original Data:**")
            st.metric("📊 Rows", len(original_data))
            st.metric("💼 Columns", len(original_data.columns))
            st.metric("🔍 Missing Values", original_data.isnull().sum().sum())
            st.metric("💾 Memory Usage", f"{original_data.memory_usage(deep=True).sum() / 1024:.1f} KB")

        with col2:
            st.markdown("**✨ Processed Data:**")
            st.metric("📊 Rows", len(final_data))
            st.metric("💼 Columns", len(final_data.columns))
            st.metric("🔍 Missing Values", final_data.isnull().sum().sum())
            st.metric("💾 Memory Usage", f"{final_data.memory_usage(deep=True).sum() / 1024:.1f} KB")

        # Applied operations log
        st.markdown("**📋 Applied Operations:**")
        if pipeline['operations_log']:
            for i, operation in enumerate(pipeline['operations_log'], 1):
                st.write(f"{i}. {operation}")
        else:
            st.info("No operations were applied (data was already clean).")

        # Final data preview
        st.markdown("**📅 Final Processed Data (First 10 Rows):**")
        st.dataframe(final_data.head(10), use_container_width=True)

        # Data types and statistics
        col3, col4 = st.columns(2)

        with col3:
            st.markdown("**📊 Data Types:**")
            dtype_info = pd.DataFrame({
                'Column': final_data.columns,
                'Data Type': final_data.dtypes.astype(str),
                'Non-Null Count': final_data.count()
            })
            st.dataframe(dtype_info, use_container_width=True)

        with col4:
            st.markdown("**📊 Statistical Summary:**")
            numerical_cols = final_data.select_dtypes(include=[np.number]).columns
            if len(numerical_cols) > 0:
                st.dataframe(final_data[numerical_cols].describe(), use_container_width=True)
            else:
                st.info("No numerical columns for statistical summary.")

        # Download options
        render_download_section(final_data, original_data)

        # Reset option
        st.markdown("---")
        if st.button("🔄 Reset Preprocessing Pipeline", type="secondary", key="reset_pipeline"):
            # Reset the entire pipeline
            original_data_reset = st.session_state.preprocessing_pipeline['original_data'].copy()
            st.session_state.preprocessing_pipeline = {
                'original_data': original_data_reset,
                'current_data': original_data_reset,
                'missing_values_handled': False,
                'encoding_applied': False,
                'outliers_handled': False,
                'scaling_applied': False,
                'operations_log': []
            }
            st.success("🔄 Preprocessing pipeline reset! You can start over from step 1.")
            st.rerun()

        # Data quality assessment
        st.markdown(f"""
        <div style="
            background: rgba(0, 130, 146, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
            border-left: 4px solid {COLORS["button_primary"]};
        ">
            <h4 style="color: {COLORS["heading"]}; margin-bottom: 1rem; font-weight: 700;">✅ Data Quality Assessment</h4>
            <p style="color: {COLORS["heading"]}; margin: 0;">
                • <strong>Missing Values:</strong> All handled ✓<br>
                • <strong>Categorical Encoding:</strong> Applied ✓<br>
                • <strong>Outliers:</strong> Processed ✓<br>
                • <strong>Feature Scaling:</strong> Applied ✓<br>
                • <strong>Ready for ML:</strong> Yes ✓
            </p>
        </div>
        """, unsafe_allow_html=True)

def render_download_section(final_data, original_data):
    """Render the download section for processed data."""
    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, {COLORS["heading"]} 0%, {COLORS["button_primary"]} 100%);
        padding: 2rem;
        border-radius: 15px;
        margin: 2rem 0;
        color: white;
        text-align: center;
    ">
        <h4 style="margin-bottom: 1rem; font-weight: 700;">💾 Download Processed Data</h4>
        <p style="opacity: 0.9; margin: 0;">Your data is ready for machine learning!</p>
    </div>
    """, unsafe_allow_html=True)

    # Download buttons
    col5, col6, col7 = st.columns(3)

    with col5:
        # CSV download
        csv_data = final_data.to_csv(index=False)
        st.download_button(
            label="💾 Download as CSV",
            data=csv_data,
            file_name="processed_laptops_data.csv",
            mime="text/csv",
            type="primary",
            use_container_width=True
        )

    with col6:
        # Excel download
        import io
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            final_data.to_excel(writer, sheet_name='Processed_Data', index=False)
            original_data.to_excel(writer, sheet_name='Original_Data', index=False)

        st.download_button(
            label="📈 Download as Excel",
            data=excel_buffer.getvalue(),
            file_name="processed_laptops_data.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            type="secondary",
            use_container_width=True
        )

    with col7:
        # JSON download
        json_data = final_data.to_json(orient='records', indent=2)
        st.download_button(
            label="📜 Download as JSON",
            data=json_data,
            file_name="processed_laptops_data.json",
            mime="application/json",
            type="secondary",
            use_container_width=True
        )
