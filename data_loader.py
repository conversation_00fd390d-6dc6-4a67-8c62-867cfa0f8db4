# ==========================
# Data Loading Functions
# ==========================

import streamlit as st
import pandas as pd
from config import DATA_CONFIG

@st.cache_data(ttl=DATA_CONFIG["cache_ttl"])
def load_data():
    """
    Load the laptop dataset with caching.
    
    Returns:
        pd.DataFrame: Loaded dataset or empty DataFrame if file not found
    """
    try:
        data = pd.read_csv(DATA_CONFIG["csv_file"])
        return data
    except FileNotFoundError:
        st.error(f"Error: File '{DATA_CONFIG['csv_file']}' not found.")
        return pd.DataFrame()
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        return pd.DataFrame()

def validate_data(data):
    """
    Validate the loaded dataset.
    
    Args:
        data (pd.DataFrame): Dataset to validate
        
    Returns:
        bool: True if data is valid, False otherwise
    """
    if data.empty:
        return False
    
    # Check for minimum required columns
    required_columns = ['Brand', 'Final Price']
    missing_columns = [col for col in required_columns if col not in data.columns]
    
    if missing_columns:
        st.warning(f"Missing required columns: {missing_columns}")
        return False
    
    return True

def get_data_info(data):
    """
    Get basic information about the dataset.
    
    Args:
        data (pd.DataFrame): Dataset to analyze
        
    Returns:
        dict: Dictionary containing dataset information
    """
    if data.empty:
        return {}
    
    info = {
        "total_rows": len(data),
        "total_columns": len(data.columns),
        "missing_values": data.isnull().sum().sum(),
        "memory_usage": data.memory_usage(deep=True).sum() / 1024,  # KB
        "numerical_columns": len(data.select_dtypes(include=['int', 'float']).columns),
        "categorical_columns": len(data.select_dtypes(include=['object']).columns)
    }
    
    # Brand-specific info
    if 'Brand' in data.columns:
        info["unique_brands"] = data['Brand'].nunique()
        info["top_brand"] = data['Brand'].value_counts().index[0] if len(data) > 0 else "N/A"
    
    # Price-specific info
    if 'Final Price' in data.columns:
        info["min_price"] = data['Final Price'].min()
        info["max_price"] = data['Final Price'].max()
        info["avg_price"] = data['Final Price'].mean()
    
    # Hardware-specific info
    if 'RAM' in data.columns:
        info["avg_ram"] = data['RAM'].mean()
    
    if 'Storage' in data.columns:
        info["avg_storage"] = data['Storage'].mean()
    
    return info
