# ==========================
# Data Visualization Tab Component
# ==========================

import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from config import COLORS
from ui.styles import create_section_header, create_info_card
from utils.visualization import (
    create_donut_chart, create_bar_chart, create_histogram, 
    create_scatter_plot, create_pie_chart, wrap_chart_in_container
)

def render_visualization_tab(data):
    """Render the data visualization tab content."""
    
    # Enhanced header
    st.markdown(create_section_header(
        "📊 Data Visualization Dashboard",
        "Comprehensive insights into laptop market trends and specifications"
    ), unsafe_allow_html=True)

    # Create sub-tabs for different visualization categories
    viz_tab1, viz_tab2, viz_tab3, viz_tab4, viz_tab5, viz_tab6 = st.tabs([
        "🏷️ Brand Insights",
        "💰 Price Analytics", 
        "💾 Hardware Specs",
        "🖥️ Display & Design",
        "🔗 Correlations",
        "📈 Market Overview"
    ])

    with viz_tab1:
        render_brand_insights(data)
    
    with viz_tab2:
        render_price_analytics(data)
    
    with viz_tab3:
        render_hardware_specs(data)
    
    with viz_tab4:
        render_display_design(data)
    
    with viz_tab5:
        render_correlations(data)
    
    with viz_tab6:
        render_market_overview(data)

def render_brand_insights(data):
    """Render brand analysis visualizations."""
    st.markdown(create_info_card(
        "🏷️ Brand Market Analysis",
        "Comprehensive analysis of laptop brands and their market presence"
    ), unsafe_allow_html=True)

    if 'Brand' in data.columns:
        # Brand Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_brands = data['Brand'].nunique()
            st.metric("🏷️ Total Brands", total_brands)

        with col2:
            top_brand = data['Brand'].value_counts().index[0]
            top_brand_count = data['Brand'].value_counts().iloc[0]
            st.metric("🏆 Leading Brand", top_brand, f"{top_brand_count} models")

        with col3:
            avg_models_per_brand = round(len(data) / data['Brand'].nunique(), 1)
            st.metric("📊 Avg Models/Brand", avg_models_per_brand)

        with col4:
            if 'Final Price' in data.columns:
                avg_price_by_brand = data.groupby('Brand')['Final Price'].mean()
                most_expensive_brand = avg_price_by_brand.idxmax()
                st.metric("💰 Premium Brand", most_expensive_brand)

        st.markdown("<br>", unsafe_allow_html=True)

        # Brand Visualizations
        col1, col2 = st.columns(2)

        with col1:
            # Top brands donut chart
            top_brands = data['Brand'].value_counts().head(8)
            fig = create_donut_chart(top_brands, "Top 8 Brands Market Share")
            
            st.markdown(wrap_chart_in_container(), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

        with col2:
            if 'Final Price' in data.columns:
                # Average price by brand
                brand_price = data.groupby('Brand')['Final Price'].agg(['mean', 'count']).reset_index()
                brand_price = brand_price[brand_price['count'] >= 5].sort_values('mean', ascending=True)
                
                if not brand_price.empty:
                    brand_price_series = pd.Series(brand_price['mean'].values, index=brand_price['Brand'])
                    fig = create_bar_chart(
                        brand_price_series, 
                        "Average Price by Brand\n(Brands with 5+ models)",
                        "Average Price ($)",
                        "",
                        horizontal=True
                    )
                    
                    st.markdown(wrap_chart_in_container(border_color=COLORS["accent"]), unsafe_allow_html=True)
                    st.pyplot(fig)
                    plt.close()

def render_price_analytics(data):
    """Render price analysis visualizations."""
    st.markdown(create_info_card(
        "💰 Price Analytics Dashboard",
        "Deep dive into laptop pricing patterns and market segments"
    ), unsafe_allow_html=True)

    if 'Final Price' in data.columns:
        # Price Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            avg_price = data['Final Price'].mean()
            st.metric("💵 Average Price", f"${avg_price:.0f}")

        with col2:
            median_price = data['Final Price'].median()
            st.metric("📈 Median Price", f"${median_price:.0f}")

        with col3:
            min_price = data['Final Price'].min()
            max_price = data['Final Price'].max()
            st.metric("📊 Price Range", f"${min_price:.0f} - ${max_price:.0f}")

        with col4:
            price_std = data['Final Price'].std()
            st.metric("📊 Price Volatility", f"${price_std:.0f}")

        st.markdown("<br>", unsafe_allow_html=True)

        # Price Visualizations
        col1, col2 = st.columns(2)

        with col1:
            # Price distribution histogram
            fig = create_histogram(data['Final Price'], "Price Distribution", "Price ($)")
            
            st.markdown(wrap_chart_in_container(), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

        with col2:
            if 'RAM' in data.columns:
                # RAM vs Price scatter plot
                fig = create_scatter_plot(
                    data['RAM'], data['Final Price'],
                    "RAM vs Price Correlation",
                    "RAM (GB)", "Price ($)",
                    data['Final Price']
                )
                
                st.markdown(wrap_chart_in_container(border_color=COLORS["accent"]), unsafe_allow_html=True)
                st.pyplot(fig)
                plt.close()

        # CPU Price Analysis
        if 'CPU' in data.columns:
            st.markdown(create_info_card(
                "💻 CPU Performance vs Price Analysis",
                "How different CPU types affect laptop pricing"
            ), unsafe_allow_html=True)
            
            # Create CPU price analysis chart
            cpu_price = data.groupby('CPU')['Final Price'].mean().sort_values(ascending=False).head(10)
            fig = create_bar_chart(cpu_price, "Average Price by CPU Type (Top 10)", "CPU Type", "Average Price ($)")
            
            st.markdown(wrap_chart_in_container(border_color=COLORS["heading"]), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

def render_hardware_specs(data):
    """Render hardware specifications analysis."""
    st.markdown(create_info_card(
        "💾 Hardware Specifications Analysis",
        "Comprehensive analysis of laptop hardware components and performance specs"
    ), unsafe_allow_html=True)

    # Hardware Overview Metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if 'RAM' in data.columns:
            avg_ram = data['RAM'].mean()
            st.metric("🧠 Average RAM", f"{avg_ram:.1f} GB")

    with col2:
        if 'Storage' in data.columns:
            avg_storage = data['Storage'].mean()
            st.metric("💾 Average Storage", f"{avg_storage:.0f} GB")

    with col3:
        if 'CPU' in data.columns:
            unique_cpus = data['CPU'].nunique()
            st.metric("💻 CPU Types", unique_cpus)

    with col4:
        if 'GPU' in data.columns:
            gpu_count = data['GPU'].notna().sum()
            st.metric("🎮 Dedicated GPUs", gpu_count)

    st.markdown("<br>", unsafe_allow_html=True)

    # Hardware Visualizations
    col1, col2 = st.columns(2)

    with col1:
        if 'RAM' in data.columns:
            # RAM distribution
            ram_counts = data['RAM'].value_counts().sort_index()
            fig = create_bar_chart(ram_counts, "RAM Distribution", "RAM (GB)", "Number of Laptops")
            
            st.markdown(wrap_chart_in_container(), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

    with col2:
        if 'Storage' in data.columns:
            # Storage distribution with categories
            storage_bins = [0, 256, 512, 1000, 2000, float('inf')]
            storage_labels = ['≤256GB', '257-512GB', '513GB-1TB', '1-2TB', '>2TB']
            data_temp = data.copy()
            data_temp['storage_category'] = pd.cut(data_temp['Storage'], bins=storage_bins, labels=storage_labels, right=False)
            
            storage_counts = data_temp['storage_category'].value_counts()
            fig = create_bar_chart(storage_counts, "Storage Distribution", "Storage Category", "Number of Laptops")
            
            st.markdown(wrap_chart_in_container(border_color=COLORS["accent"]), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

    # CPU Analysis
    if 'CPU' in data.columns:
        st.markdown(create_info_card(
            "💻 CPU Performance Analysis",
            "Distribution of CPU brands and types in the dataset"
        ), unsafe_allow_html=True)

        # CPU brand distribution
        cpu_brands = data['CPU'].str.extract(r'(Intel|AMD)')[0].value_counts()

        if not cpu_brands.empty:
            fig = create_pie_chart(cpu_brands, "CPU Brand Distribution")
            
            st.markdown(wrap_chart_in_container(border_color=COLORS["heading"]), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

    # Numerical Feature Distributions
    render_numerical_distributions(data)

def render_numerical_distributions(data):
    """Render histograms for all numerical features."""
    st.markdown(create_info_card(
        "📈 Hardware Feature Distributions",
        "Statistical distribution of all numerical hardware specifications"
    ), unsafe_allow_html=True)

    num_features = data.select_dtypes(include=['int', 'float']).columns
    if len(num_features) > 0:
        for i in range(0, len(num_features), 2):
            cols = st.columns(2)
            for j in range(2):
                if i+j < len(num_features):
                    with cols[j]:
                        feature_name = num_features[i+j]
                        fig = create_histogram(
                            data[feature_name], 
                            f'Distribution of {feature_name}',
                            feature_name
                        )
                        
                        st.markdown(wrap_chart_in_container(border_color=COLORS["heading"]), unsafe_allow_html=True)
                        st.pyplot(fig)
                        plt.close()
    else:
        st.warning("⚠️ No numerical features found in data")

def render_display_design(data):
    """Render display and design analysis."""
    st.markdown(create_info_card(
        "🖥️ Display & Design Features",
        "Analysis of screen specifications and design characteristics"
    ), unsafe_allow_html=True)

    # Display Overview Metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if 'Screen' in data.columns:
            avg_screen = data['Screen'].mean()
            st.metric("🖥️ Average Screen", f"{avg_screen:.1f}\"")

    with col2:
        if 'Touch' in data.columns:
            touch_count = data['Touch'].sum() if data['Touch'].dtype in ['int64', 'float64'] else 0
            touch_pct = (touch_count / len(data)) * 100
            st.metric("🔍 Touchscreen", f"{touch_pct:.1f}%")

    with col3:
        if 'Screen' in data.columns:
            screen_sizes = data['Screen'].nunique()
            st.metric("📏 Screen Sizes", screen_sizes)

    with col4:
        if 'Brand' in data.columns:
            design_variety = data['Brand'].nunique()
            st.metric("🎨 Design Variety", f"{design_variety} brands")

    st.markdown("<br>", unsafe_allow_html=True)

    # Display Visualizations
    col1, col2 = st.columns(2)

    with col1:
        if 'Screen' in data.columns:
            # Screen size distribution
            screen_counts = data['Screen'].value_counts().sort_index()
            fig = create_bar_chart(screen_counts, "Screen Size Distribution", "Screen Size (inches)", "Number of Laptops")
            
            st.markdown(wrap_chart_in_container(), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

    with col2:
        if all(col in data.columns for col in ['Screen', 'Final Price']):
            # Screen size vs Price analysis
            screen_price = data.groupby('Screen')['Final Price'].mean().sort_index()
            fig = create_bar_chart(screen_price, "Average Price by Screen Size", "Screen Size (inches)", "Average Price ($)")
            
            st.markdown(wrap_chart_in_container(border_color=COLORS["accent"]), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

    # Touchscreen Analysis
    if 'Touch' in data.columns:
        st.markdown(create_info_card(
            "🔍 Touchscreen Technology Analysis",
            "Impact of touchscreen technology on laptop pricing and market share"
        ), unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            # Touchscreen distribution
            touch_counts = data['Touch'].value_counts()
            labels = ['Non-Touchscreen', 'Touchscreen']
            fig = create_pie_chart(pd.Series(touch_counts.values, index=labels), "Touchscreen Distribution")
            
            st.markdown(wrap_chart_in_container(), unsafe_allow_html=True)
            st.pyplot(fig)
            plt.close()

        with col2:
            if 'Final Price' in data.columns:
                # Touchscreen impact on price
                touch_price = data.groupby('Touch')['Final Price'].mean()
                labels = ['Non-Touchscreen', 'Touchscreen']
                touch_price_series = pd.Series(touch_price.values, index=labels)
                fig = create_bar_chart(touch_price_series, "Price Impact of Touchscreen", "", "Average Price ($)")
                
                st.markdown(wrap_chart_in_container(border_color=COLORS["accent"]), unsafe_allow_html=True)
                st.pyplot(fig)
                plt.close()

def render_correlations(data):
    """Render correlation analysis."""
    st.markdown(create_info_card(
        "🔗 Feature Correlations",
        "Correlation analysis between different laptop specifications"
    ), unsafe_allow_html=True)

    # Get numerical columns for correlation
    numerical_cols = data.select_dtypes(include=['int', 'float']).columns
    
    if len(numerical_cols) > 1:
        # Correlation heatmap
        fig, ax = plt.subplots(figsize=(12, 8))
        fig.patch.set_facecolor(COLORS["background"])
        
        correlation_matrix = data[numerical_cols].corr()
        
        sns.heatmap(correlation_matrix, annot=True, cmap='RdYlBu_r', center=0,
                   square=True, ax=ax, cbar_kws={'shrink': 0.8})
        
        ax.set_title('Feature Correlation Matrix', fontsize=16, weight='bold', color=COLORS["heading"])
        plt.tight_layout()
        
        st.markdown(wrap_chart_in_container(border_color=COLORS["heading"]), unsafe_allow_html=True)
        st.pyplot(fig)
        plt.close()
        
        # Pair plot for key features
        if len(numerical_cols) >= 3:
            st.markdown(create_info_card(
                "📊 Pair Plot Analysis",
                "Pairwise relationships between key numerical features"
            ), unsafe_allow_html=True)
            
            # Select top 4 numerical features for pair plot
            key_features = numerical_cols[:4]
            
            fig = plt.figure(figsize=(12, 10))
            fig.patch.set_facecolor(COLORS["background"])
            
            # Create pair plot manually
            n_features = len(key_features)
            for i in range(n_features):
                for j in range(n_features):
                    ax = plt.subplot(n_features, n_features, i * n_features + j + 1)
                    
                    if i == j:
                        # Diagonal: histogram
                        ax.hist(data[key_features[i]].dropna(), bins=20, alpha=0.7, color=COLORS["button_primary"])
                        ax.set_title(key_features[i], fontsize=10, color=COLORS["heading"])
                    else:
                        # Off-diagonal: scatter plot
                        ax.scatter(data[key_features[j]], data[key_features[i]], 
                                 alpha=0.5, color=COLORS["button_primary"], s=20)
                        
                    ax.tick_params(colors=COLORS["heading"], labelsize=8)
                    for spine in ax.spines.values():
                        spine.set_color(COLORS["heading"])
            
            plt.tight_layout()
            st.pyplot(fig)
            plt.close()
    else:
        st.warning("⚠️ Not enough numerical features for correlation analysis")

def render_market_overview(data):
    """Render market overview and summary statistics."""
    st.markdown(create_info_card(
        "📈 Market Overview",
        "Comprehensive market analysis and key insights"
    ), unsafe_allow_html=True)

    # Market summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("📊 Total Products", len(data))

    with col2:
        if 'Brand' in data.columns:
            st.metric("🏷️ Brands", data['Brand'].nunique())

    with col3:
        if 'Final Price' in data.columns:
            avg_price = data['Final Price'].mean()
            st.metric("💰 Avg Price", f"${avg_price:.0f}")

    with col4:
        missing_pct = (data.isnull().sum().sum() / (len(data) * len(data.columns))) * 100
        st.metric("📊 Data Quality", f"{100-missing_pct:.1f}%")

    # Market insights
    st.markdown("### 🔍 Key Market Insights")
    
    insights = []
    
    if 'Brand' in data.columns:
        top_brand = data['Brand'].value_counts().index[0]
        top_brand_share = (data['Brand'].value_counts().iloc[0] / len(data)) * 100
        insights.append(f"🏆 **{top_brand}** dominates the market with {top_brand_share:.1f}% market share")
    
    if 'Final Price' in data.columns:
        price_median = data['Final Price'].median()
        insights.append(f"💰 Median laptop price is **${price_median:.0f}**")
        
        if 'RAM' in data.columns:
            ram_median = data['RAM'].median()
            insights.append(f"🧠 Most laptops come with **{ram_median:.0f}GB RAM**")
    
    if 'Screen' in data.columns:
        popular_screen = data['Screen'].mode().iloc[0] if not data['Screen'].mode().empty else "N/A"
        insights.append(f"📺 Most popular screen size is **{popular_screen}\"**")
    
    for insight in insights:
        st.markdown(f"- {insight}")
    
    # Data quality summary
    st.markdown("### 📊 Data Quality Summary")
    
    quality_data = []
    for col in data.columns:
        missing_count = data[col].isnull().sum()
        missing_pct = (missing_count / len(data)) * 100
        quality_data.append({
            'Column': col,
            'Missing Values': missing_count,
            'Missing %': f"{missing_pct:.1f}%",
            'Data Type': str(data[col].dtype)
        })
    
    quality_df = pd.DataFrame(quality_data)
    st.dataframe(quality_df, use_container_width=True)
