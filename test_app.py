#!/usr/bin/env python3
"""
Test script for the enhanced laptop recommendation system
"""

import subprocess
import sys
import time

def test_app():
    """Test the enhanced laptop recommendation app"""
    print("🚀 Testing Enhanced Laptop Recommendation System")
    print("=" * 50)
    
    # Test imports
    try:
        print("📦 Testing imports...")
        import pandas as pd
        import numpy as np
        import streamlit as st
        from sklearn.neighbors import KNeighborsClassifier
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.svm import SVC
        print("✅ All imports successful!")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test data loading
    try:
        print("📊 Testing data loading...")
        data = pd.read_csv("laptops.csv")
        print(f"✅ Data loaded successfully! Shape: {data.shape}")
        print(f"📋 Columns: {list(data.columns)}")
    except FileNotFoundError:
        print("❌ laptops.csv not found!")
        return False
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        return False
    
    # Test preprocessing functions
    try:
        print("🔧 Testing preprocessing functions...")
        
        # Import our functions
        sys.path.append('.')
        from DD import extract_cpu_features, extract_gpu_features, create_performance_score
        
        # Test CPU feature extraction
        cpu_features = extract_cpu_features(data['CPU'])
        print(f"✅ CPU features extracted: {cpu_features.columns.tolist()}")
        
        # Test GPU feature extraction
        gpu_features = extract_gpu_features(data['GPU'])
        print(f"✅ GPU features extracted: {gpu_features.columns.tolist()}")
        
        print("✅ All preprocessing functions working!")
        
    except Exception as e:
        print(f"❌ Preprocessing error: {e}")
        return False
    
    print("\n🎉 All tests passed! Your enhanced laptop recommendation system is ready!")
    print("\n🚀 To run the app, use: streamlit run DD.py")
    
    return True

if __name__ == "__main__":
    test_app()
