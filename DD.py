# ==========================
# Import Libraries
# ==========================
import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer, KNNImputer

# Handle IterativeImputer import for different sklearn versions
try:
    from sklearn.impute import IterativeImputer
except ImportError:
    IterativeImputer = None

from sklearn.metrics import precision_score, recall_score, f1_score, classification_report
from sklearn.model_selection import train_test_split
from scipy.stats import zscore
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import RFE
from sklearn.metrics import confusion_matrix
from sklearn.metrics import roc_curve, auc

# ==========================
# Streamlit Page Configuration
# ==========================
st.set_page_config(page_title="Laptop Recommendation System",
                   page_icon="💻",
                   layout="wide")

# ==========================
# Load Dataset
# ==========================
@st.cache_data
def load_data():
    try:
        data = pd.read_csv("laptops.csv")
        return data
    except FileNotFoundError:
        st.error("Error: File 'laptops.csv' not found.")
        return pd.DataFrame()

data = load_data()

if data.empty:
    st.stop()

# ==========================
# Main Application Logic
# ==========================
# Set page background and global styles
st.markdown("""
<style>
/* Global Styles */
.main .block-container {
    background-color: #F0EFED;
    padding-top: 2rem;
}

/* Custom CSS for Modern Calm Theme */
.stApp {
    background-color: #F0EFED;
}

/* Improved text contrast */
.stMarkdown {
    color: #32127A;
}

/* Better visibility for metric labels */
.metric-label {
    color: #32127A !important;
    font-weight: 600 !important;
}

/* Fix metric card text visibility */
.stMetric {
    background-color: rgba(255, 255, 255, 0.9) !important;
    padding: 1rem !important;
    border-radius: 10px !important;
    box-shadow: 0 2px 8px rgba(50, 18, 122, 0.1) !important;
}

.stMetric > div {
    color: #32127A !important;
}

.stMetric label {
    color: #32127A !important;
    font-weight: 700 !important;
    font-size: 0.9rem !important;
}

.stMetric [data-testid="metric-container"] {
    background-color: rgba(255, 255, 255, 0.9) !important;
    padding: 1rem !important;
    border-radius: 10px !important;
    border-left: 4px solid #008292 !important;
}

.stMetric [data-testid="metric-container"] > div {
    color: #32127A !important;
}

.stMetric [data-testid="metric-container"] label {
    color: #32127A !important;
    font-weight: 700 !important;
}

/* Force metric text visibility */
.stMetric [data-testid="metric-container"] [data-testid="metric-value"] {
    color: #32127A !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
}

.stMetric [data-testid="metric-container"] [data-testid="metric-delta"] {
    color: #008292 !important;
    font-weight: 600 !important;
}

/* Alternative approach for metric styling */
div[data-testid="metric-container"] {
    background: rgba(255, 255, 255, 0.95) !important;
    padding: 1.2rem !important;
    border-radius: 12px !important;
    border-left: 4px solid #008292 !important;
    box-shadow: 0 4px 12px rgba(50, 18, 122, 0.1) !important;
}

div[data-testid="metric-container"] * {
    color: #32127A !important;
}

/* Enhanced readability for cards */
.modern-card {
    color: #32127A !important;
}

/* Improved contrast for data displays */
.stDataFrame {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(50, 18, 122, 0.1);
}

/* Data Cleaning tab text improvements */
.stSelectbox label {
    color: #32127A !important;
    font-weight: 600 !important;
}

.stMultiSelect label {
    color: #32127A !important;
    font-weight: 600 !important;
}

.stRadio label {
    color: #32127A !important;
    font-weight: 600 !important;
}

.stSlider label {
    color: #32127A !important;
    font-weight: 600 !important;
}

/* Improve button styling */
.stButton > button {
    background-color: #008292 !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
}

.stButton > button:hover {
    background-color: #32127A !important;
    color: white !important;
}

/* Tab Styling */
.stTabs [data-baseweb="tab-list"] {
    gap: 8px;
    justify-content: center;
    margin-bottom: 2rem;
    background-color: transparent;
    padding: 1rem;
    border-radius: 20px;
    background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%);
    box-shadow: 0 8px 32px rgba(0, 130, 146, 0.1);
}

.stTabs [data-baseweb="tab"] {
    height: 65px;
    padding: 0px 28px;
    background-color: #F0EFED;
    border-radius: 15px;
    color: #32127A;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid #CDE0E1;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 4px;
    box-shadow: 0 4px 12px rgba(50, 18, 122, 0.1);
}

.stTabs [aria-selected="true"] {
    background: linear-gradient(135deg, #008292 0%, #32127A 100%);
    color: white;
    border-color: #008292;
    box-shadow: 0 8px 25px rgba(0, 130, 146, 0.4);
    transform: translateY(-2px);
}

.stTabs [data-baseweb="tab"]:hover {
    background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(179, 68, 108, 0.2);
    border-color: #B3446C;
}

/* Title Styling */
.main-title {
    background: linear-gradient(135deg, #32127A 0%, #008292 50%, #B3446C 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 3.5rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 1rem;
    letter-spacing: -0.02em;
}

.subtitle {
    color: #32127A;
    font-size: 1.3rem;
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 500;
    opacity: 0.8;
}

/* Card Styles */
.modern-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 6px 24px rgba(50, 18, 122, 0.08);
    border: 1px solid rgba(227, 204, 220, 0.3);
    transition: all 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 130, 146, 0.15);
}

/* Button Styles */
.stButton > button {
    background: linear-gradient(135deg, #008292 0%, #B3446C 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 130, 146, 0.3);
}

.stButton > button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(179, 68, 108, 0.4);
    background: linear-gradient(135deg, #B3446C 0%, #32127A 100%);
}

/* Metric Cards */
.metric-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(227, 204, 220, 0.3) 100%);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid rgba(205, 224, 225, 0.5);
    box-shadow: 0 8px 32px rgba(50, 18, 122, 0.08);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 130, 146, 0.15);
}
</style>
""", unsafe_allow_html=True)

# Modern title with gradient text
st.markdown("""
<div style="text-align: center; margin-bottom: 3rem; padding: 2rem; background: linear-gradient(135deg, rgba(227, 204, 220, 0.3) 0%, rgba(205, 224, 225, 0.3) 100%); border-radius: 25px; backdrop-filter: blur(10px);">
    <h1 class="main-title">💻 Laptop Recommendation System</h1>
    <p class="subtitle">Discover your perfect laptop with our AI-powered recommendation platform</p>
</div>
""", unsafe_allow_html=True)

# Create navigation tabs
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "👋 Welcome",
    "📊 Data Visualization",
    "🧹 Data Cleaning Insights",
    "🤖 Model Evaluation",
    "💻 Laptop Recommendation"
])

with tab1:
    # Hero Section with Modern Calm Theme
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 3rem 2rem;
        border-radius: 25px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 15px 40px rgba(50, 18, 122, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    ">
        <h1 style="font-size: 2.8rem; margin-bottom: 1rem; font-weight: 700; letter-spacing: -0.02em;">👋 Welcome to Your Tech Journey!</h1>
        <p style="font-size: 1.4rem; margin-bottom: 1.5rem; opacity: 0.95; line-height: 1.6;">Find your perfect laptop with our intelligent AI-powered recommendation system</p>
    </div>
    """, unsafe_allow_html=True)

    # Dataset Overview Section
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        border-left: 5px solid #008292;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(50, 18, 122, 0.1);
    ">
        <h2 style="color: #32127A; margin-bottom: 1.5rem; display: flex; align-items: center; font-weight: 700;">
            <span style="margin-right: 0.5rem;">📊</span> Dataset Overview
        </h2>
        <p style="color: #32127A; opacity: 0.8; font-size: 1.1rem; margin: 0;">Comprehensive laptop data analysis for informed decision-making</p>
    </div>
    """, unsafe_allow_html=True)

    # Dataset Statistics
    if not data.empty:
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            total_laptops = len(data)
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #32127A 0%, #008292 100%);
                color: white;
                transform: scale(1);
                transition: all 0.3s ease;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💻</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{total_laptops:,}</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">Total Laptops</div>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            unique_brands = data['Brand'].nunique() if 'Brand' in data.columns else 0
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #B3446C 0%, #32127A 100%);
                color: white;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">🏷️</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{unique_brands}</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">Unique Brands</div>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            if 'Final Price' in data.columns:
                min_price = int(data['Final Price'].min())
                max_price = int(data['Final Price'].max())
                price_range = f"${min_price:,} - ${max_price:,}"
            else:
                price_range = "N/A"
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #008292 0%, #CDE0E1 100%);
                color: #32127A;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💰</div>
                <div style="font-size: 1.2rem; font-weight: bold;">{price_range}</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">Price Range</div>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            if 'RAM' in data.columns:
                avg_ram = int(data['RAM'].mean())
                ram_info = f"{avg_ram} GB"
            else:
                ram_info = "N/A"
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #E3CCDC 0%, #B3446C 100%);
                color: white;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">🧠</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{ram_info}</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">Avg RAM</div>
            </div>
            """, unsafe_allow_html=True)

        with col5:
            if 'Storage' in data.columns:
                avg_storage = int(data['Storage'].mean())
                storage_info = f"{avg_storage} GB"
            else:
                storage_info = "N/A"
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #CDE0E1 0%, #008292 100%);
                color: #32127A;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💾</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{storage_info}</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">Avg Storage</div>
            </div>
            """, unsafe_allow_html=True)



    # Features Section
    st.markdown("""
    <div style="margin: 3rem 0 2rem 0;">
        <h3 style="color: #32127A; text-align: center; margin-bottom: 2rem; font-weight: 700; font-size: 2rem;">🚀 Platform Features</h3>
        <p style="color: #32127A; text-align: center; opacity: 0.7; font-size: 1.1rem; margin-bottom: 2rem;">Powerful tools designed for the modern tech community</p>
    </div>
    """, unsafe_allow_html=True)

    # Feature cards with enhanced design
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #008292 0%, #32127A 100%);
            color: white;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">📊</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Data Visualization</h4>
            </div>
            <p style="margin: 0; opacity: 0.95; line-height: 1.6; font-size: 1rem;">Explore comprehensive charts and graphs to understand laptop market trends, brand distributions, and feature correlations.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #CDE0E1 0%, #008292 100%);
            color: #32127A;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🤖</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Model Evaluation</h4>
            </div>
            <p style="margin: 0; opacity: 0.9; line-height: 1.6; font-size: 1rem;">Evaluate machine learning models with detailed performance metrics including accuracy, precision, recall, and F1-score.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #B3446C 0%, #32127A 100%);
            color: white;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🧹</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Data Cleaning</h4>
            </div>
            <p style="margin: 0; opacity: 0.95; line-height: 1.6; font-size: 1rem;">Clean and preprocess data with advanced tools for handling missing values, outliers, duplicates, and feature engineering.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #E3CCDC 0%, #B3446C 100%);
            color: #32127A;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">💻</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Laptop Recommendation</h4>
            </div>
            <p style="margin: 0; opacity: 0.9; line-height: 1.6; font-size: 1rem;">Get personalized laptop recommendations based on your budget, usage requirements, and preferred specifications.</p>
        </div>
        """, unsafe_allow_html=True)

    # Call to Action
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2.5rem;
        border-radius: 20px;
        text-align: center;
        margin: 2rem 0;
        color: white;
        box-shadow: 0 12px 40px rgba(50, 18, 122, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    ">
        <h3 style="margin-bottom: 1rem; font-size: 1.8rem; font-weight: 700;">🎆 Ready to Find Your Perfect Laptop?</h3>
        <p style="margin-bottom: 2rem; opacity: 0.95; font-size: 1.2rem; line-height: 1.5;">Navigate through our tabs to explore data insights or jump directly to our recommendation engine!</p>
        <div style="display: flex; justify-content: center; gap: 1.5rem; flex-wrap: wrap;">
            <div style="background: rgba(227, 204, 220, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">📊 Analyze Data</div>
            <div style="background: rgba(205, 224, 225, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">🧹 Clean Data</div>
            <div style="background: rgba(179, 68, 108, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">🤖 Evaluate Models</div>
            <div style="background: rgba(0, 130, 146, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">💻 Get Recommendations</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

with tab2:
    # Enhanced header with modern styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">📊 Data Visualization Dashboard</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">Comprehensive insights into laptop market trends and specifications</p>
    </div>
    """, unsafe_allow_html=True)

    viz_tab1, viz_tab2, viz_tab3, viz_tab4, viz_tab5, viz_tab6 = st.tabs([
        "🏷️ Brand Insights",
        "💰 Price Analytics",
        "💾 Hardware Specs",
        "🖥️ Display & Design",
        "🔗 Correlations",
        "📈 Market Overview"
    ])

    with viz_tab1:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">🏷️ Brand Market Analysis</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive analysis of laptop brands and their market presence</p>
        </div>
        """, unsafe_allow_html=True)

        # Brand Overview Metrics
        if 'Brand' in data.columns:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_brands = data['Brand'].nunique()
                st.metric("🏷️ Total Brands", total_brands)

            with col2:
                top_brand = data['Brand'].value_counts().index[0]
                top_brand_count = data['Brand'].value_counts().iloc[0]
                st.metric("🏆 Leading Brand", top_brand, f"{top_brand_count} models")

            with col3:
                avg_models_per_brand = round(len(data) / data['Brand'].nunique(), 1)
                st.metric("📊 Avg Models/Brand", avg_models_per_brand)

            with col4:
                if 'Final Price' in data.columns:
                    avg_price_by_brand = data.groupby('Brand')['Final Price'].mean()
                    most_expensive_brand = avg_price_by_brand.idxmax()
                    st.metric("💰 Premium Brand", most_expensive_brand)

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Brand Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'Brand' in data.columns:
                # Enhanced donut chart with better styling
                top_brands = data['Brand'].value_counts().head(8)

                # Set matplotlib style for better integration
                plt.style.use('default')
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')  # Match background

                # Use your theme colors
                colors = ['#32127A', '#008292', '#B3446C', '#E3CCDC', '#CDE0E1', '#F0EFED', '#667eea', '#764ba2']

                wedges, texts, autotexts = ax.pie(
                    top_brands.values,
                    labels=top_brands.index,
                    autopct='%1.1f%%',
                    colors=colors,
                    startangle=90,
                    pctdistance=0.85,
                    textprops={'fontsize': 11, 'weight': 'bold', 'color': '#32127A'}
                )

                # Improve text contrast
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
                    autotext.set_fontsize(10)

                for text in texts:
                    text.set_color('#32127A')
                    text.set_fontweight('bold')
                    text.set_fontsize(10)

                # Create donut effect
                centre_circle = plt.Circle((0,0), 0.70, fc='#F0EFED')
                fig.gca().add_artist(centre_circle)

                ax.set_title("Top 8 Brands Market Share", fontsize=16, weight='bold', color='#32127A', pad=20)
                plt.tight_layout()

                # Add container styling
                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)

                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if 'Brand' in data.columns and 'Final Price' in data.columns:
                # Enhanced bar chart with better styling
                brand_price = data.groupby('Brand')['Final Price'].agg(['mean', 'count']).reset_index()
                brand_price = brand_price[brand_price['count'] >= 5].sort_values('mean', ascending=True)

                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')  # Match background

                bars = ax.barh(brand_price['Brand'], brand_price['mean'],
                              color='#008292', alpha=0.8, edgecolor='#32127A', linewidth=1.5)

                # Improve text styling
                ax.set_xlabel('Average Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Average Price by Brand\n(Brands with 5+ models)', fontsize=14, weight='bold', color='#32127A')
                ax.grid(axis='x', alpha=0.3, color='#32127A')

                # Style the axes
                ax.tick_params(colors='#32127A', labelsize=10)
                ax.spines['bottom'].set_color('#32127A')
                ax.spines['left'].set_color('#32127A')
                ax.spines['top'].set_visible(False)
                ax.spines['right'].set_visible(False)

                # Add value labels with better contrast
                for i, bar in enumerate(bars):
                    width = bar.get_width()
                    ax.text(width + 20, bar.get_y() + bar.get_height()/2,
                           f'${width:.0f}', ha='left', va='center', fontweight='bold', color='#32127A')

                plt.tight_layout()

                # Add container styling
                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)

                st.markdown("</div>", unsafe_allow_html=True)

    with viz_tab2:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">💰 Price Analytics Dashboard</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Deep dive into laptop pricing patterns and market segments</p>
        </div>
        """, unsafe_allow_html=True)

        # Price Overview Metrics
        if 'Final Price' in data.columns:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                avg_price = data['Final Price'].mean()
                st.metric("💵 Average Price", f"${avg_price:.0f}")

            with col2:
                median_price = data['Final Price'].median()
                st.metric("📈 Median Price", f"${median_price:.0f}")

            with col3:
                min_price = data['Final Price'].min()
                max_price = data['Final Price'].max()
                st.metric("� Price Range", f"${min_price:.0f} - ${max_price:.0f}")

            with col4:
                price_std = data['Final Price'].std()
                st.metric("📊 Price Volatility", f"${price_std:.0f}")

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Price Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'Final Price' in data.columns:
                # Price distribution histogram
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                n, bins, patches = ax.hist(data['Final Price'], bins=30, alpha=0.8, color='#008292', edgecolor='#32127A')

                ax.set_xlabel('Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Price Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if all(col in data.columns for col in ['RAM', 'Final Price']):
                # RAM vs Price scatter plot
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                scatter = ax.scatter(data['RAM'], data['Final Price'],
                                   c=data['Final Price'], cmap='viridis',
                                   alpha=0.6, s=60, edgecolors='#32127A', linewidth=0.5)

                ax.set_xlabel('RAM (GB)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('RAM vs Price Correlation', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.colorbar(scatter, label='Price ($)')
                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # CPU Price Analysis
        if all(col in data.columns for col in ['CPU', 'Final Price']):
            fig, ax = plt.subplots(figsize=(16, 8))
            fig.patch.set_facecolor('#F0EFED')

            sns.barplot(data=data, x='CPU', y='Final Price', estimator='mean', ax=ax, palette='viridis')
            ax.set_title('Average Final Price by CPU Type', fontsize=16, weight='bold', color='#32127A')
            ax.set_xlabel('CPU Type', fontsize=12, weight='bold', color='#32127A')
            ax.set_ylabel('Average Final Price ($)', fontsize=12, weight='bold', color='#32127A')
            ax.tick_params(colors='#32127A', labelsize=9)
            ax.grid(True, alpha=0.3, color='#32127A')

            for spine in ax.spines.values():
                spine.set_color('#32127A')

            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            st.markdown("""
            <div style="
                background: rgba(255, 255, 255, 0.9);
                padding: 1rem;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                border-left: 4px solid #32127A;
                margin-bottom: 1rem;
            ">
            """, unsafe_allow_html=True)

            st.pyplot(fig)
            st.markdown("</div>", unsafe_allow_html=True)

    with viz_tab3:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">💾 Hardware Specifications Analysis</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive analysis of laptop hardware components and performance specs</p>
        </div>
        """, unsafe_allow_html=True)

        # Hardware Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if 'RAM' in data.columns:
                avg_ram = data['RAM'].mean()
                st.metric("🧠 Average RAM", f"{avg_ram:.1f} GB")

        with col2:
            if 'Storage' in data.columns:
                avg_storage = data['Storage'].mean()
                st.metric("💾 Average Storage", f"{avg_storage:.0f} GB")

        with col3:
            if 'CPU' in data.columns:
                unique_cpus = data['CPU'].nunique()
                st.metric("💻 CPU Types", unique_cpus)

        with col4:
            if 'GPU' in data.columns:
                gpu_count = data['GPU'].notna().sum()
                st.metric("🎮 Dedicated GPUs", gpu_count)

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Hardware Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'RAM' in data.columns:
                # RAM distribution
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                ram_counts = data['RAM'].value_counts().sort_index()
                bars = ax.bar(ram_counts.index, ram_counts.values, color='#008292', alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('RAM (GB)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('RAM Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels on bars
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                           f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if 'Storage' in data.columns:
                # Storage distribution
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                # Create storage bins
                storage_bins = [0, 256, 512, 1000, 2000, float('inf')]
                storage_labels = ['≤256GB', '257-512GB', '513GB-1TB', '1-2TB', '>2TB']
                data['storage_category'] = pd.cut(data['Storage'], bins=storage_bins, labels=storage_labels, right=False)

                storage_counts = data['storage_category'].value_counts()
                colors = ['#CDE0E1', '#008292', '#B3446C', '#32127A', '#E3CCDC']

                bars = ax.bar(range(len(storage_counts)), storage_counts.values,
                             color=colors[:len(storage_counts)], alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('Storage Category', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Storage Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.set_xticks(range(len(storage_counts)))
                ax.set_xticklabels(storage_counts.index, rotation=45)
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                           f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # CPU Analysis
        if 'CPU' in data.columns:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.5rem; border-radius: 15px; margin: 2rem 0;">
                <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">💻 CPU Performance Analysis</h4>
            </div>
            """, unsafe_allow_html=True)

            # CPU brand distribution
            cpu_brands = data['CPU'].str.extract(r'(Intel|AMD)')[0].value_counts()

            if not cpu_brands.empty:
                fig, ax = plt.subplots(figsize=(8, 6))
                fig.patch.set_facecolor('#F0EFED')

                colors = ['#32127A', '#008292']
                wedges, texts, autotexts = ax.pie(cpu_brands.values, labels=cpu_brands.index,
                                                 autopct='%1.1f%%', colors=colors, startangle=90)

                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')

                for text in texts:
                    text.set_color('#32127A')
                    text.set_fontweight('bold')

                ax.set_title('CPU Brand Distribution', fontsize=14, weight='bold', color='#32127A')

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #32127A;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # Numerical Feature Distributions (Histograms)
        st.markdown("""
        <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.5rem; border-radius: 15px; margin: 2rem 0;">
            <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">📈 Hardware Feature Distributions</h4>
        </div>
        """, unsafe_allow_html=True)

        num_features = data.select_dtypes(include=['int', 'float']).columns
        if len(num_features) > 0:
            for i in range(0, len(num_features), 2):
                cols = st.columns(2)
                for j in range(2):
                    if i+j < len(num_features):
                        with cols[j]:
                            fig, ax = plt.subplots(figsize=(10, 6))
                            fig.patch.set_facecolor('#F0EFED')

                            # Enhanced histogram with KDE
                            feature_name = num_features[i+j]
                            ax.hist(data[feature_name].dropna(), bins=20, alpha=0.7, color='#008292', edgecolor='#32127A')

                            # Add KDE line if seaborn is available
                            try:
                                ax2 = ax.twinx()
                                data[feature_name].dropna().plot.kde(ax=ax2, color='#B3446C', linewidth=2)
                                ax2.set_ylabel('Density', color='#32127A', fontweight='bold')
                                ax2.tick_params(colors='#32127A')
                                ax2.spines['right'].set_color('#32127A')
                            except:
                                pass

                            ax.set_xlabel(feature_name, fontsize=12, weight='bold', color='#32127A')
                            ax.set_ylabel('Frequency', fontsize=12, weight='bold', color='#32127A')
                            ax.set_title(f'Distribution of {feature_name}', fontsize=14, weight='bold', color='#32127A')
                            ax.grid(True, alpha=0.3, color='#32127A')
                            ax.tick_params(colors='#32127A')

                            for spine in ax.spines.values():
                                spine.set_color('#32127A')

                            plt.tight_layout()

                            st.markdown("""
                            <div style="
                                background: rgba(255, 255, 255, 0.9);
                                padding: 1rem;
                                border-radius: 15px;
                                box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                                border-left: 4px solid #32127A;
                                margin-bottom: 1rem;
                            ">
                            """, unsafe_allow_html=True)

                            st.pyplot(fig)
                            st.markdown("</div>", unsafe_allow_html=True)
        else:
            st.warning("⚠️ No numerical features found in data")

    with viz_tab4:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">�️ Display & Design Features</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Analysis of screen specifications and design characteristics</p>
        </div>
        """, unsafe_allow_html=True)

        # Display Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if 'Screen' in data.columns:
                avg_screen = data['Screen'].mean()
                st.metric("🖥️ Average Screen", f"{avg_screen:.1f}\"")

        with col2:
            if 'Touch' in data.columns:
                touch_count = data['Touch'].sum() if data['Touch'].dtype in ['int64', 'float64'] else 0
                touch_pct = (touch_count / len(data)) * 100
                st.metric("🔍 Touchscreen", f"{touch_pct:.1f}%")

        with col3:
            if 'Screen' in data.columns:
                screen_sizes = data['Screen'].nunique()
                st.metric("📏 Screen Sizes", screen_sizes)

        with col4:
            if 'Brand' in data.columns:
                design_variety = data['Brand'].nunique()
                st.metric("🎨 Design Variety", f"{design_variety} brands")

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Display Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'Screen' in data.columns:
                # Screen size distribution
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                screen_counts = data['Screen'].value_counts().sort_index()
                bars = ax.bar(screen_counts.index, screen_counts.values, color='#008292', alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('Screen Size (inches)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Screen Size Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                           f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if all(col in data.columns for col in ['Screen', 'Final Price']):
                # Screen size vs Price analysis
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                # Group by screen size and calculate average price
                screen_price = data.groupby('Screen')['Final Price'].mean().sort_index()

                bars = ax.bar(screen_price.index, screen_price.values, color='#B3446C', alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('Screen Size (inches)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Average Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Average Price by Screen Size', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                           f'${height:.0f}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # Additional Display Analysis
        if 'Touch' in data.columns:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.5rem; border-radius: 15px; margin: 2rem 0;">
                <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">🔍 Touchscreen Technology Analysis</h4>
            </div>
            """, unsafe_allow_html=True)

            col1, col2 = st.columns(2)

            with col1:
                # Touchscreen vs Non-touchscreen pie chart
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                touch_counts = data['Touch'].value_counts()
                labels = ['Non-Touchscreen', 'Touchscreen']
                colors = ['#CDE0E1', '#32127A']

                wedges, texts, autotexts = ax.pie(touch_counts.values, labels=labels,
                                                 autopct='%1.1f%%', colors=colors, startangle=90)

                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')

                for text in texts:
                    text.set_color('#32127A')
                    text.set_fontweight('bold')

                ax.set_title('Touchscreen vs Non-Touchscreen Distribution', fontsize=14, weight='bold', color='#32127A')

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

            with col2:
                if all(col in data.columns for col in ['Touch', 'Final Price']):
                    # Touchscreen impact on price
                    fig, ax = plt.subplots(figsize=(10, 8))
                    fig.patch.set_facecolor('#F0EFED')

                    touch_price = data.groupby('Touch')['Final Price'].mean()
                    labels = ['Non-Touchscreen', 'Touchscreen']
                    colors = ['#CDE0E1', '#32127A']

                    bars = ax.bar(labels, touch_price.values, color=colors, alpha=0.8, edgecolor='#32127A')

                    ax.set_ylabel('Average Price ($)', fontsize=12, weight='bold', color='#32127A')
                    ax.set_title('Price Impact of Touchscreen Technology', fontsize=14, weight='bold', color='#32127A')
                    ax.grid(True, alpha=0.3, color='#32127A')
                    ax.tick_params(colors='#32127A')

                    # Add value labels
                    for bar in bars:
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                               f'${height:.0f}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                    for spine in ax.spines.values():
                        spine.set_color('#32127A')

                    plt.tight_layout()

                    st.markdown("""
                    <div style="
                        background: rgba(255, 255, 255, 0.9);
                        padding: 1rem;
                        border-radius: 15px;
                        box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                        border-left: 4px solid #B3446C;
                        margin-bottom: 1rem;
                    ">
                    """, unsafe_allow_html=True)

                    st.pyplot(fig)
                    st.markdown("</div>", unsafe_allow_html=True)

    with viz_tab5:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">🔗 Feature Correlations</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Explore relationships and correlations between different laptop features</p>
        </div>
        """, unsafe_allow_html=True)

        # Correlation Analysis
        numeric_data = data.select_dtypes(include=['number'])
        if len(numeric_data.columns) > 1:
            selected_corr_cols = st.multiselect(
                "Select columns for correlation matrix:",
                numeric_data.columns.tolist(),
                default=numeric_data.columns.tolist()[:4]
            )

            if len(selected_corr_cols) >= 2:
                fig, ax = plt.subplots(figsize=(12, 8))
                fig.patch.set_facecolor('#F0EFED')

                corr = numeric_data[selected_corr_cols].corr()
                sns.heatmap(corr, annot=True, square=True, cmap='RdYlBu_r', center=0, ax=ax)

                ax.set_title('Feature Correlation Matrix', fontsize=14, weight='bold', color='#32127A')
                ax.tick_params(colors='#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

                # Add Pair Plot Section
                st.markdown("<br>", unsafe_allow_html=True)
                st.markdown("""
                <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.2rem; border-radius: 12px; margin: 1.5rem 0;">
                    <h4 style="color: #32127A; margin-bottom: 0.8rem; font-weight: 700;">🔗 Pair Plot Analysis</h4>
                    <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive pairwise relationships between numerical features</p>
                </div>
                """, unsafe_allow_html=True)

                # Select features for pair plot
                pair_plot_cols = st.multiselect(
                    "Select features for pair plot (max 5 for performance):",
                    selected_corr_cols,
                    default=selected_corr_cols[:4] if len(selected_corr_cols) >= 4 else selected_corr_cols
                )

                if len(pair_plot_cols) >= 2 and len(pair_plot_cols) <= 5:
                    try:
                        # Create pair plot
                        fig, axes = plt.subplots(len(pair_plot_cols), len(pair_plot_cols),
                                               figsize=(15, 15))
                        fig.patch.set_facecolor('#F0EFED')

                        for i, col1 in enumerate(pair_plot_cols):
                            for j, col2 in enumerate(pair_plot_cols):
                                ax = axes[i, j] if len(pair_plot_cols) > 1 else axes

                                if i == j:
                                    # Diagonal: histogram
                                    ax.hist(numeric_data[col1].dropna(), bins=20,
                                           alpha=0.7, color='#008292', edgecolor='#32127A')
                                    ax.set_title(f'{col1} Distribution', fontsize=10,
                                               weight='bold', color='#32127A')
                                else:
                                    # Off-diagonal: scatter plot
                                    ax.scatter(numeric_data[col2], numeric_data[col1],
                                             alpha=0.6, s=30, color='#B3446C',
                                             edgecolors='#32127A', linewidth=0.5)

                                    # Add correlation coefficient
                                    corr_coef = numeric_data[col1].corr(numeric_data[col2])
                                    ax.text(0.05, 0.95, f'r={corr_coef:.2f}',
                                           transform=ax.transAxes, fontsize=9,
                                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                                           color='#32127A', weight='bold')

                                # Styling
                                ax.tick_params(colors='#32127A', labelsize=8)
                                ax.grid(True, alpha=0.3, color='#32127A')

                                for spine in ax.spines.values():
                                    spine.set_color('#32127A')

                                if i == len(pair_plot_cols) - 1:
                                    ax.set_xlabel(col2, fontsize=10, weight='bold', color='#32127A')
                                if j == 0:
                                    ax.set_ylabel(col1, fontsize=10, weight='bold', color='#32127A')

                        plt.suptitle('Feature Pair Plot Analysis', fontsize=16,
                                   weight='bold', color='#32127A', y=0.98)
                        plt.tight_layout()

                        st.markdown("""
                        <div style="
                            background: rgba(255, 255, 255, 0.9);
                            padding: 1rem;
                            border-radius: 15px;
                            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                            border-left: 4px solid #B3446C;
                            margin-bottom: 1rem;
                        ">
                        """, unsafe_allow_html=True)

                        st.pyplot(fig)
                        st.markdown("</div>", unsafe_allow_html=True)

                        # Add interpretation
                        st.markdown("""
                        <div style="background: rgba(179, 68, 108, 0.1); padding: 1rem; border-radius: 10px; margin-top: 1rem;">
                            <h5 style="color: #32127A; margin-bottom: 0.5rem;">📊 Pair Plot Interpretation:</h5>
                            <ul style="color: #32127A; margin: 0;">
                                <li><strong>Diagonal plots:</strong> Show the distribution of each feature</li>
                                <li><strong>Scatter plots:</strong> Show relationships between feature pairs</li>
                                <li><strong>Correlation values (r):</strong> Range from -1 to 1, closer to ±1 indicates stronger relationship</li>
                                <li><strong>Patterns:</strong> Look for linear trends, clusters, or outliers</li>
                            </ul>
                        </div>
                        """, unsafe_allow_html=True)

                    except Exception as e:
                        st.error(f"❌ Error generating pair plot: {str(e)}")
                        st.info("💡 Try selecting fewer features or check for missing values.")

                elif len(pair_plot_cols) > 5:
                    st.warning("⚠️ Please select maximum 5 features for optimal performance.")
                elif len(pair_plot_cols) < 2:
                    st.info("ℹ️ Please select at least 2 features for pair plot analysis.")
            else:
                st.info("ℹ️ Please select at least two columns for correlation matrix.")
        else:
            st.warning("⚠️ Not enough numerical features for correlation matrix")

    with viz_tab6:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">📈 Market Overview</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive market analysis and trends in the laptop industry</p>
        </div>
        """, unsafe_allow_html=True)

        # Market Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_models = len(data)
            st.metric("💻 Total Models", total_models)

        with col2:
            if 'Final Price' in data.columns:
                market_value = data['Final Price'].sum()
                st.metric("💰 Market Value", f"${market_value/1000000:.1f}M")

        with col3:
            if 'Brand' in data.columns:
                market_leaders = data['Brand'].nunique()
                st.metric("🏆 Market Players", market_leaders)

        with col4:
            if 'Final Price' in data.columns:
                avg_market_price = data['Final Price'].mean()
                st.metric("📈 Avg Market Price", f"${avg_market_price:.0f}")

        st.markdown("<br>", unsafe_allow_html=True)

        # Market Segmentation
        if 'Final Price' in data.columns:
            fig, ax = plt.subplots(figsize=(12, 6))
            fig.patch.set_facecolor('#F0EFED')

            # Define market segments
            budget = len(data[data['Final Price'] < 500])
            mid_range = len(data[(data['Final Price'] >= 500) & (data['Final Price'] < 1000)])
            premium = len(data[(data['Final Price'] >= 1000) & (data['Final Price'] < 1500)])
            luxury = len(data[data['Final Price'] >= 1500])

            segments = ['Budget\n(<$500)', 'Mid-Range\n($500-$999)', 'Premium\n($1000-$1499)', 'Luxury\n($1500+)']
            sizes = [budget, mid_range, premium, luxury]
            colors = ['#CDE0E1', '#008292', '#B3446C', '#32127A']

            bars = ax.bar(segments, sizes, color=colors, alpha=0.8, edgecolor='#32127A')

            ax.set_xlabel('Market Segment', fontsize=12, weight='bold', color='#32127A')
            ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
            ax.set_title('Market Segmentation by Price Range', fontsize=14, weight='bold', color='#32127A')
            ax.grid(True, alpha=0.3, color='#32127A')
            ax.tick_params(colors='#32127A')

            # Add value labels
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                       f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

            for spine in ax.spines.values():
                spine.set_color('#32127A')

            plt.tight_layout()

            st.markdown("""
            <div style="
                background: rgba(255, 255, 255, 0.9);
                padding: 1rem;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                border-left: 4px solid #B3446C;
                margin-bottom: 1rem;
            ">
            """, unsafe_allow_html=True)

            st.pyplot(fig)
            st.markdown("</div>", unsafe_allow_html=True)

with tab3:
    # Enhanced header with modern styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">🧹 Data Preprocessing Pipeline</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">Complete step-by-step data preprocessing workflow</p>
    </div>
    """, unsafe_allow_html=True)

    # Create preprocessing subtabs
    prep_tab1, prep_tab2, prep_tab3, prep_tab4, prep_tab5 = st.tabs([
        "🔍 Handle Missing Values",
        "🔠 Encoding",
        "🎯 Handle Outliers",
        "⚖️ Scaling",
        "📦 Final Processed Data"
    ])

    # Initialize preprocessing pipeline in session state
    if 'preprocessing_pipeline' not in st.session_state:
        st.session_state.preprocessing_pipeline = {
            'original_data': data.copy(),
            'current_data': data.copy(),
            'missing_values_handled': False,
            'encoding_applied': False,
            'outliers_handled': False,
            'scaling_applied': False,
            'operations_log': []
        }

    # Tab 1: Handle Missing Values
    with prep_tab1:
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #008292;
            margin-bottom: 2rem;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">🔍 Handle Missing Values</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Step 1: Clean and handle missing values in your dataset</p>
        </div>
        """, unsafe_allow_html=True)

        current_data = st.session_state.preprocessing_pipeline['current_data'].copy()

        # Show current missing values status
        missing_data = current_data.isnull().sum()
        missing_percent = (missing_data / len(current_data)) * 100

        missing_df = pd.DataFrame({
            'Column': missing_data.index,
            'Missing Count': missing_data.values,
            'Missing Percentage': missing_percent.values
        }).sort_values('Missing Count', ascending=False)

        # Display missing values summary
        st.markdown("**📊 Missing Values Summary:**")
        missing_cols_display = missing_df[missing_df['Missing Count'] > 0]
        if not missing_cols_display.empty:
            st.dataframe(missing_cols_display, use_container_width=True)
        else:
            st.success("✅ No missing values found!")

        # Show first 5 rows of current data
        st.markdown("**📅 Current Data (First 5 Rows):**")
        st.dataframe(current_data.head(), use_container_width=True)

        if missing_df['Missing Count'].sum() > 0:
            # Get columns with missing values
            missing_cols_list = current_data.columns[current_data.isnull().any()].tolist()

            # Operation selection
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**📊 Select Operation:**")
                operation = st.selectbox(
                    "Choose operation to apply:",
                    ["Simple Imputer", "KNN Imputer", "Iterative Imputer", "Fill with Value"],
                    key="missing_operation"
                )

            with col2:
                st.markdown("**💼 Select Column:**")
                selected_col = st.selectbox(
                    "Choose column to process:",
                    missing_cols_list,
                    key="missing_column"
                )

            # Operation-specific parameters
            if operation == "Simple Imputer":
                strategy = st.selectbox(
                    "Imputation strategy:",
                    ["mean", "median", "most_frequent", "constant"],
                    key="missing_strategy"
                )
                if strategy == "constant":
                    fill_value = st.text_input(
                        "Constant value to fill:",
                        value="0",
                        key="missing_fill_value"
                    )
                else:
                    fill_value = None

            elif operation == "KNN Imputer":
                n_neighbors = st.slider(
                    "Number of neighbors:",
                    min_value=1,
                    max_value=10,
                    value=3,
                    key="missing_knn_neighbors"
                )

            elif operation == "Iterative Imputer":
                col3, col4 = st.columns(2)
                with col3:
                    max_iter = st.slider(
                        "Maximum iterations:",
                        min_value=5,
                        max_value=20,
                        value=10,
                        key="missing_iter_max"
                    )
                with col4:
                    random_state = st.number_input(
                        "Random state:",
                        value=42,
                        key="missing_iter_random"
                    )

            elif operation == "Fill with Value":
                custom_value = st.text_input(
                    "Value to fill missing data:",
                    value="",
                    key="missing_custom_value"
                )

            # Apply operation button
            if st.button(f"🚀 Apply {operation}", type="primary", key="apply_missing_values"):
                with st.spinner(f"🔄 Applying {operation}..."):
                    try:
                        success = False
                        operation_description = ""

                        if operation == "Simple Imputer":
                            from sklearn.impute import SimpleImputer
                            imputer = SimpleImputer(
                                strategy=strategy,
                                fill_value=fill_value if strategy == "constant" else None
                            )
                            current_data[[selected_col]] = imputer.fit_transform(current_data[[selected_col]])
                            operation_description = f"Simple Imputer ({strategy}) on {selected_col}"
                            success = True

                        elif operation == "KNN Imputer":
                            if current_data[selected_col].dtype in ['int64', 'float64']:
                                from sklearn.impute import KNNImputer
                                imputer = KNNImputer(n_neighbors=n_neighbors)
                                current_data[[selected_col]] = imputer.fit_transform(current_data[[selected_col]])
                                operation_description = f"KNN Imputer (k={n_neighbors}) on {selected_col}"
                                success = True
                            else:
                                st.error("KNN Imputer can only be applied to numerical columns.")

                        elif operation == "Iterative Imputer":
                            if current_data[selected_col].dtype in ['int64', 'float64']:
                                try:
                                    from sklearn.experimental import enable_iterative_imputer
                                    from sklearn.impute import IterativeImputer
                                    imputer = IterativeImputer(max_iter=max_iter, random_state=random_state)
                                    current_data[[selected_col]] = imputer.fit_transform(current_data[[selected_col]])
                                    operation_description = f"Iterative Imputer (max_iter={max_iter}) on {selected_col}"
                                    success = True
                                except ImportError:
                                    st.error("Iterative Imputer not available in this sklearn version.")
                            else:
                                st.error("Iterative Imputer can only be applied to numerical columns.")

                        elif operation == "Fill with Value":
                            if custom_value:
                                current_data[selected_col] = current_data[selected_col].fillna(custom_value)
                                operation_description = f"Fill with '{custom_value}' on {selected_col}"
                                success = True
                            else:
                                st.error("Please provide a value to fill.")

                        if success:
                            # Update session state
                            st.session_state.preprocessing_pipeline['current_data'] = current_data
                            st.session_state.preprocessing_pipeline['operations_log'].append(operation_description)

                            # Check if all missing values are handled
                            remaining_missing = current_data.isnull().sum().sum()
                            if remaining_missing == 0:
                                st.session_state.preprocessing_pipeline['missing_values_handled'] = True
                                st.balloons()
                                st.success("🎉 All missing values handled! You can now proceed to the Encoding tab.")
                            else:
                                st.success(f"✅ {operation} applied successfully! {remaining_missing} missing values remaining.")

                            st.rerun()

                    except Exception as e:
                        st.error(f"❌ Error applying {operation}: {str(e)}")

        else:
            st.session_state.preprocessing_pipeline['missing_values_handled'] = True
            st.success("✅ No missing values found! You can proceed to the Encoding tab.")

        # Show operations log
        if st.session_state.preprocessing_pipeline['operations_log']:
            st.markdown("**📋 Applied Operations:**")
            for i, op in enumerate(st.session_state.preprocessing_pipeline['operations_log'], 1):
                st.write(f"{i}. {op}")

    # Tab 2: Encoding
    with prep_tab2:
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #B3446C;
            margin-bottom: 2rem;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">🔠 Encoding</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Step 2: Encode categorical variables for machine learning</p>
        </div>
        """, unsafe_allow_html=True)

        # Check if missing values are handled
        if not st.session_state.preprocessing_pipeline['missing_values_handled']:
            st.warning("⚠️ Please complete missing value handling first before proceeding to encoding.")
            st.info("📍 Go to the 'Handle Missing Values' tab to complete step 1.")
        else:
            current_data = st.session_state.preprocessing_pipeline['current_data'].copy()

            # Show first 5 rows of current data
            st.markdown("**📅 Current Data (First 5 Rows):**")
            st.dataframe(current_data.head(), use_container_width=True)

            # Identify categorical columns
            categorical_cols = current_data.select_dtypes(include=['object', 'category']).columns.tolist()

            if categorical_cols:
                st.markdown("**📊 Categorical Columns Found:**")
                cat_info = []
                for col in categorical_cols:
                    unique_count = current_data[col].nunique()
                    cat_info.append({
                        'Column': col,
                        'Unique Values': unique_count,
                        'Sample Values': ', '.join(current_data[col].dropna().unique()[:3].astype(str))
                    })

                cat_df = pd.DataFrame(cat_info)
                st.dataframe(cat_df, use_container_width=True)

                # Encoding options
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**📊 Select Encoding Method:**")
                    encoding_method = st.selectbox(
                        "Choose encoding method:",
                        ["Label Encoding", "One-Hot Encoding"],
                        key="encoding_method"
                    )

                with col2:
                    st.markdown("**💼 Select Columns:**")
                    selected_cols_encoding = st.multiselect(
                        "Choose columns to encode:",
                        categorical_cols,
                        default=categorical_cols[:3] if len(categorical_cols) >= 3 else categorical_cols,
                        key="encoding_columns"
                    )

                # Show encoding method information
                if encoding_method == "Label Encoding":
                    st.info("📍 **Label Encoding**: Converts categories to numerical labels (0, 1, 2, ...). Best for ordinal data.")
                else:
                    st.info("📍 **One-Hot Encoding**: Creates binary columns for each category. Best for nominal data.")

                # Apply encoding button
                if st.button(f"🚀 Apply {encoding_method}", type="primary", key="apply_encoding"):
                    if selected_cols_encoding:
                        with st.spinner(f"🔄 Applying {encoding_method}..."):
                            try:
                                success = False
                                operation_description = ""

                                if encoding_method == "Label Encoding":
                                    from sklearn.preprocessing import LabelEncoder
                                    for col in selected_cols_encoding:
                                        le = LabelEncoder()
                                        current_data[col] = le.fit_transform(current_data[col].astype(str))
                                    operation_description = f"Label Encoding on {', '.join(selected_cols_encoding)}"
                                    success = True

                                elif encoding_method == "One-Hot Encoding":
                                    from sklearn.preprocessing import OneHotEncoder
                                    import pandas as pd

                                    # Apply one-hot encoding
                                    ohe = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
                                    ohe_encoded = ohe.fit_transform(current_data[selected_cols_encoding])

                                    # Create new column names
                                    feature_names = ohe.get_feature_names_out(selected_cols_encoding)
                                    ohe_df = pd.DataFrame(ohe_encoded, columns=feature_names, index=current_data.index)

                                    # Drop original columns and add encoded ones
                                    current_data = current_data.drop(selected_cols_encoding, axis=1)
                                    current_data = pd.concat([current_data, ohe_df], axis=1)

                                    operation_description = f"One-Hot Encoding on {', '.join(selected_cols_encoding)}"
                                    success = True

                                if success:
                                    # Update session state
                                    st.session_state.preprocessing_pipeline['current_data'] = current_data
                                    st.session_state.preprocessing_pipeline['operations_log'].append(operation_description)
                                    st.session_state.preprocessing_pipeline['encoding_applied'] = True

                                    st.balloons()
                                    st.success(f"✅ {encoding_method} applied successfully! You can now proceed to the Handle Outliers tab.")
                                    st.rerun()

                            except Exception as e:
                                st.error(f"❌ Error applying {encoding_method}: {str(e)}")
                    else:
                        st.warning("⚠️ Please select at least one column to encode.")

            else:
                st.session_state.preprocessing_pipeline['encoding_applied'] = True
                st.success("✅ No categorical columns found! You can proceed to the Handle Outliers tab.")

            # Show current data info after encoding
            if st.session_state.preprocessing_pipeline['encoding_applied']:
                st.markdown("**📊 Data Info After Encoding:**")
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("📊 Total Columns", len(current_data.columns))
                with col2:
                    st.metric("💼 Numerical Columns", len(current_data.select_dtypes(include=['int64', 'float64']).columns))
                with col3:
                    st.metric("🔠 Categorical Columns", len(current_data.select_dtypes(include=['object', 'category']).columns))

    # Tab 3: Handle Outliers
    with prep_tab3:
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #32127A;
            margin-bottom: 2rem;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">🎯 Handle Outliers</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Step 3: Detect and handle outliers in numerical data</p>
        </div>
        """, unsafe_allow_html=True)

        # Check if encoding is completed
        if not st.session_state.preprocessing_pipeline['encoding_applied']:
            st.warning("⚠️ Please complete encoding first before proceeding to outlier handling.")
            st.info("📍 Go to the 'Encoding' tab to complete step 2.")
        else:
            current_data = st.session_state.preprocessing_pipeline['current_data'].copy()

            # Show first 5 rows of current data
            st.markdown("**📅 Current Data (First 5 Rows):**")
            st.dataframe(current_data.head(), use_container_width=True)

            # Get numerical columns
            import numpy as np
            numerical_cols = current_data.select_dtypes(include=[np.number]).columns.tolist()

            if numerical_cols:
                st.markdown("**📊 Numerical Columns Available:**")
                st.write(f"Found {len(numerical_cols)} numerical columns: {', '.join(numerical_cols)}")

                # Outlier detection and handling options
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**🔍 Detection Method:**")
                    detection_method = st.selectbox(
                        "Choose outlier detection method:",
                        ["IQR Method", "Z-Score Method"],
                        key="outlier_detection_method"
                    )

                with col2:
                    st.markdown("**💼 Select Column:**")
                    selected_col_outlier = st.selectbox(
                        "Choose column to analyze:",
                        numerical_cols,
                        key="outlier_column"
                    )

                # Detection parameters
                if detection_method == "Z-Score Method":
                    z_threshold = st.slider(
                        "Z-Score threshold (values beyond this are outliers):",
                        min_value=1.0,
                        max_value=4.0,
                        value=3.0,
                        step=0.1,
                        key="z_threshold"
                    )

                # Detect outliers button
                if st.button("🔍 Detect Outliers", type="secondary", key="detect_outliers"):
                    with st.spinner("🔄 Detecting outliers..."):
                        try:
                            col_data = current_data[selected_col_outlier].dropna()

                            if detection_method == "IQR Method":
                                Q1 = col_data.quantile(0.25)
                                Q3 = col_data.quantile(0.75)
                                IQR = Q3 - Q1
                                lower_bound = Q1 - 1.5 * IQR
                                upper_bound = Q3 + 1.5 * IQR
                                outliers_mask = (col_data < lower_bound) | (col_data > upper_bound)
                                outliers = col_data[outliers_mask]

                                st.info(f"📊 **IQR Method Results:**")
                                st.write(f"Q1: {Q1:.2f}, Q3: {Q3:.2f}, IQR: {IQR:.2f}")
                                st.write(f"Lower bound: {lower_bound:.2f}, Upper bound: {upper_bound:.2f}")

                            else:  # Z-Score Method
                                from scipy.stats import zscore
                                z_scores = zscore(col_data)
                                outliers_mask = np.abs(z_scores) > z_threshold
                                outliers = col_data[outliers_mask]

                                st.info(f"📊 **Z-Score Method Results:**")
                                st.write(f"Threshold: {z_threshold}")

                            outlier_count = len(outliers)
                            outlier_percentage = (outlier_count / len(col_data)) * 100

                            if outlier_count > 0:
                                st.warning(f"⚠️ Found {outlier_count} outliers ({outlier_percentage:.1f}% of data)")

                                # Show outlier statistics
                                col_stats1, col_stats2, col_stats3 = st.columns(3)
                                with col_stats1:
                                    st.metric("📊 Total Values", len(col_data))
                                with col_stats2:
                                    st.metric("⚠️ Outliers Found", outlier_count)
                                with col_stats3:
                                    st.metric("📊 Percentage", f"{outlier_percentage:.1f}%")

                                # Store outlier info in session state for handling
                                st.session_state['outlier_info'] = {
                                    'column': selected_col_outlier,
                                    'method': detection_method,
                                    'outliers_mask': outliers_mask,
                                    'outlier_count': outlier_count
                                }
                            else:
                                st.success(f"✅ No outliers detected in {selected_col_outlier} using {detection_method}!")
                                st.session_state['outlier_info'] = None

                        except Exception as e:
                            st.error(f"❌ Error detecting outliers: {str(e)}")

                # Outlier handling options (only show if outliers detected)
                if 'outlier_info' in st.session_state and st.session_state['outlier_info'] is not None:
                    st.markdown("**🔧 Outlier Handling Options:**")

                    handling_method = st.selectbox(
                        "Choose how to handle outliers:",
                        ["Remove Outliers", "Cap Outliers (Winsorization)", "Transform with Log"],
                        key="outlier_handling_method"
                    )

                    if handling_method == "Cap Outliers (Winsorization)":
                        col_win1, col_win2 = st.columns(2)
                        with col_win1:
                            lower_percentile = st.slider(
                                "Lower percentile to cap:",
                                min_value=0.01,
                                max_value=0.10,
                                value=0.05,
                                step=0.01,
                                key="lower_percentile"
                            )
                        with col_win2:
                            upper_percentile = st.slider(
                                "Upper percentile to cap:",
                                min_value=0.90,
                                max_value=0.99,
                                value=0.95,
                                step=0.01,
                                key="upper_percentile"
                            )

                    # Apply outlier handling
                    if st.button(f"🚀 Apply {handling_method}", type="primary", key="apply_outlier_handling"):
                        with st.spinner(f"🔄 Applying {handling_method}..."):
                            try:
                                outlier_info = st.session_state['outlier_info']
                                col_name = outlier_info['column']
                                success = False
                                operation_description = ""

                                if handling_method == "Remove Outliers":
                                    # Remove rows with outliers
                                    outliers_mask = outlier_info['outliers_mask']
                                    # Get the full dataframe mask
                                    full_mask = current_data[col_name].isin(current_data[col_name][~outliers_mask])
                                    current_data = current_data[full_mask]
                                    operation_description = f"Removed {outlier_info['outlier_count']} outliers from {col_name}"
                                    success = True

                                elif handling_method == "Cap Outliers (Winsorization)":
                                    # Cap outliers using percentiles
                                    lower_cap = current_data[col_name].quantile(lower_percentile)
                                    upper_cap = current_data[col_name].quantile(upper_percentile)
                                    current_data[col_name] = current_data[col_name].clip(lower=lower_cap, upper=upper_cap)
                                    operation_description = f"Winsorized {col_name} (capped at {lower_percentile:.2f}-{upper_percentile:.2f} percentiles)"
                                    success = True

                                elif handling_method == "Transform with Log":
                                    # Apply log transformation (add 1 to handle zeros)
                                    if (current_data[col_name] > 0).all():
                                        current_data[col_name] = np.log(current_data[col_name])
                                        operation_description = f"Applied log transformation to {col_name}"
                                        success = True
                                    else:
                                        current_data[col_name] = np.log1p(current_data[col_name])
                                        operation_description = f"Applied log1p transformation to {col_name}"
                                        success = True

                                if success:
                                    # Update session state
                                    st.session_state.preprocessing_pipeline['current_data'] = current_data
                                    st.session_state.preprocessing_pipeline['operations_log'].append(operation_description)
                                    st.session_state.preprocessing_pipeline['outliers_handled'] = True

                                    # Clear outlier info
                                    st.session_state['outlier_info'] = None

                                    st.balloons()
                                    st.success(f"✅ {handling_method} applied successfully! You can now proceed to the Scaling tab.")
                                    st.rerun()

                            except Exception as e:
                                st.error(f"❌ Error applying {handling_method}: {str(e)}")

                # Option to skip outlier handling
                if st.button("⏭️ Skip Outlier Handling", type="secondary", key="skip_outliers"):
                    st.session_state.preprocessing_pipeline['outliers_handled'] = True
                    st.info("📍 Outlier handling skipped. You can now proceed to the Scaling tab.")
                    st.rerun()

            else:
                st.session_state.preprocessing_pipeline['outliers_handled'] = True
                st.success("✅ No numerical columns found! You can proceed to the Scaling tab.")

    # Tab 4: Scaling
    with prep_tab4:
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #CDE0E1;
            margin-bottom: 2rem;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">⚖️ Scaling</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Step 4: Scale numerical features for optimal machine learning performance</p>
        </div>
        """, unsafe_allow_html=True)

        # Check if outlier handling is completed
        if not st.session_state.preprocessing_pipeline['outliers_handled']:
            st.warning("⚠️ Please complete outlier handling first before proceeding to scaling.")
            st.info("📍 Go to the 'Handle Outliers' tab to complete step 3.")
        else:
            current_data = st.session_state.preprocessing_pipeline['current_data'].copy()

            # Show first 5 rows of current data
            st.markdown("**📅 Current Data (First 5 Rows):**")
            st.dataframe(current_data.head(), use_container_width=True)

            # Get numerical columns
            numerical_cols = current_data.select_dtypes(include=[np.number]).columns.tolist()

            if numerical_cols:
                st.markdown("**📊 Numerical Columns Available for Scaling:**")
                st.write(f"Found {len(numerical_cols)} numerical columns: {', '.join(numerical_cols)}")

                # Show data statistics before scaling
                st.markdown("**📊 Data Statistics (Before Scaling):**")
                stats_df = current_data[numerical_cols].describe()
                st.dataframe(stats_df, use_container_width=True)

                # Scaling options
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**📊 Select Scaling Method:**")
                    scaling_method = st.selectbox(
                        "Choose scaling method:",
                        ["Standard Scaler", "MinMax Scaler", "Robust Scaler", "Normalizer"],
                        key="scaling_method"
                    )

                with col2:
                    st.markdown("**💼 Select Columns:**")
                    selected_cols_scaling = st.multiselect(
                        "Choose columns to scale:",
                        numerical_cols,
                        default=numerical_cols,
                        key="scaling_columns"
                    )

                # Show scaling method information
                scaling_info = {
                    "Standard Scaler": "Standardizes features by removing mean and scaling to unit variance (z-score normalization). Best for normally distributed data.",
                    "MinMax Scaler": "Scales features to a fixed range [0,1]. Preserves relationships but sensitive to outliers.",
                    "Robust Scaler": "Uses median and IQR for scaling. Robust to outliers but may not preserve exact relationships.",
                    "Normalizer": "Scales individual samples to have unit norm. Good for text analysis and when direction matters more than magnitude."
                }

                st.info(f"📍 **{scaling_method}**: {scaling_info[scaling_method]}")

                # Apply scaling button
                if st.button(f"🚀 Apply {scaling_method}", type="primary", key="apply_scaling"):
                    if selected_cols_scaling:
                        with st.spinner(f"🔄 Applying {scaling_method}..."):
                            try:
                                success = False
                                operation_description = ""

                                if scaling_method == "Standard Scaler":
                                    from sklearn.preprocessing import StandardScaler
                                    scaler = StandardScaler()
                                    current_data[selected_cols_scaling] = scaler.fit_transform(current_data[selected_cols_scaling])
                                    operation_description = f"Standard Scaler applied to {', '.join(selected_cols_scaling)}"
                                    success = True

                                elif scaling_method == "MinMax Scaler":
                                    from sklearn.preprocessing import MinMaxScaler
                                    scaler = MinMaxScaler()
                                    current_data[selected_cols_scaling] = scaler.fit_transform(current_data[selected_cols_scaling])
                                    operation_description = f"MinMax Scaler applied to {', '.join(selected_cols_scaling)}"
                                    success = True

                                elif scaling_method == "Robust Scaler":
                                    from sklearn.preprocessing import RobustScaler
                                    scaler = RobustScaler()
                                    current_data[selected_cols_scaling] = scaler.fit_transform(current_data[selected_cols_scaling])
                                    operation_description = f"Robust Scaler applied to {', '.join(selected_cols_scaling)}"
                                    success = True

                                elif scaling_method == "Normalizer":
                                    from sklearn.preprocessing import Normalizer
                                    scaler = Normalizer()
                                    current_data[selected_cols_scaling] = scaler.fit_transform(current_data[selected_cols_scaling])
                                    operation_description = f"Normalizer applied to {', '.join(selected_cols_scaling)}"
                                    success = True

                                if success:
                                    # Update session state
                                    st.session_state.preprocessing_pipeline['current_data'] = current_data
                                    st.session_state.preprocessing_pipeline['operations_log'].append(operation_description)
                                    st.session_state.preprocessing_pipeline['scaling_applied'] = True

                                    st.balloons()
                                    st.success(f"✅ {scaling_method} applied successfully! You can now view the final processed data.")

                                    # Show statistics after scaling
                                    st.markdown("**📊 Data Statistics (After Scaling):**")
                                    stats_after = current_data[selected_cols_scaling].describe()
                                    st.dataframe(stats_after, use_container_width=True)

                                    st.rerun()

                            except Exception as e:
                                st.error(f"❌ Error applying {scaling_method}: {str(e)}")
                    else:
                        st.warning("⚠️ Please select at least one column to scale.")

                # Option to skip scaling
                if st.button("⏭️ Skip Scaling", type="secondary", key="skip_scaling"):
                    st.session_state.preprocessing_pipeline['scaling_applied'] = True
                    st.info("📍 Scaling skipped. You can now view the final processed data.")
                    st.rerun()

            else:
                st.session_state.preprocessing_pipeline['scaling_applied'] = True
                st.success("✅ No numerical columns found! You can view the final processed data.")

    # Tab 5: Final Processed Data
    with prep_tab5:
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #E3CCDC;
            margin-bottom: 2rem;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">📦 Final Processed Data</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Step 5: Review and download your fully processed dataset</p>
        </div>
        """, unsafe_allow_html=True)

        # Check if all preprocessing steps are completed
        pipeline = st.session_state.preprocessing_pipeline
        all_steps_completed = (
            pipeline['missing_values_handled'] and
            pipeline['encoding_applied'] and
            pipeline['outliers_handled'] and
            pipeline['scaling_applied']
        )

        if not all_steps_completed:
            st.warning("⚠️ Please complete all preprocessing steps before viewing the final data.")

            # Show progress
            st.markdown("**📊 Preprocessing Progress:**")
            progress_items = [
                ("🔍 Handle Missing Values", pipeline['missing_values_handled']),
                ("🔠 Encoding", pipeline['encoding_applied']),
                ("🎯 Handle Outliers", pipeline['outliers_handled']),
                ("⚖️ Scaling", pipeline['scaling_applied'])
            ]

            for step_name, completed in progress_items:
                if completed:
                    st.success(f"✅ {step_name} - Completed")
                else:
                    st.error(f"❌ {step_name} - Pending")

        else:
            # All steps completed - show final data
            final_data = pipeline['current_data'].copy()
            original_data = pipeline['original_data'].copy()

            st.success("🎉 All preprocessing steps completed successfully!")

            # Data transformation summary
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%);
                padding: 2rem;
                border-radius: 15px;
                margin: 2rem 0;
                text-align: center;
            ">
                <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">📊 Data Transformation Summary</h4>
            </div>
            """, unsafe_allow_html=True)

            # Before vs After comparison
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**📅 Original Data:**")
                st.metric("📊 Rows", len(original_data))
                st.metric("💼 Columns", len(original_data.columns))
                st.metric("🔍 Missing Values", original_data.isnull().sum().sum())
                st.metric("💾 Memory Usage", f"{original_data.memory_usage(deep=True).sum() / 1024:.1f} KB")

            with col2:
                st.markdown("**✨ Processed Data:**")
                st.metric("📊 Rows", len(final_data))
                st.metric("💼 Columns", len(final_data.columns))
                st.metric("🔍 Missing Values", final_data.isnull().sum().sum())
                st.metric("💾 Memory Usage", f"{final_data.memory_usage(deep=True).sum() / 1024:.1f} KB")

            # Applied operations log
            st.markdown("**📋 Applied Operations:**")
            if pipeline['operations_log']:
                for i, operation in enumerate(pipeline['operations_log'], 1):
                    st.write(f"{i}. {operation}")
            else:
                st.info("No operations were applied (data was already clean).")

            # Final data preview
            st.markdown("**📅 Final Processed Data (First 10 Rows):**")
            st.dataframe(final_data.head(10), use_container_width=True)

            # Data types and statistics
            col3, col4 = st.columns(2)

            with col3:
                st.markdown("**📊 Data Types:**")
                dtype_info = pd.DataFrame({
                    'Column': final_data.columns,
                    'Data Type': final_data.dtypes.astype(str),
                    'Non-Null Count': final_data.count()
                })
                st.dataframe(dtype_info, use_container_width=True)

            with col4:
                st.markdown("**📊 Statistical Summary:**")
                numerical_cols = final_data.select_dtypes(include=[np.number]).columns
                if len(numerical_cols) > 0:
                    st.dataframe(final_data[numerical_cols].describe(), use_container_width=True)
                else:
                    st.info("No numerical columns for statistical summary.")

            # Download options
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #32127A 0%, #008292 100%);
                padding: 2rem;
                border-radius: 15px;
                margin: 2rem 0;
                color: white;
                text-align: center;
            ">
                <h4 style="margin-bottom: 1rem; font-weight: 700;">💾 Download Processed Data</h4>
                <p style="opacity: 0.9; margin: 0;">Your data is ready for machine learning!</p>
            </div>
            """, unsafe_allow_html=True)

            # Download buttons
            col5, col6, col7 = st.columns(3)

            with col5:
                # CSV download
                csv_data = final_data.to_csv(index=False)
                st.download_button(
                    label="💾 Download as CSV",
                    data=csv_data,
                    file_name="processed_laptops_data.csv",
                    mime="text/csv",
                    type="primary",
                    use_container_width=True
                )

            with col6:
                # Excel download
                import io
                excel_buffer = io.BytesIO()
                with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                    final_data.to_excel(writer, sheet_name='Processed_Data', index=False)
                    original_data.to_excel(writer, sheet_name='Original_Data', index=False)

                st.download_button(
                    label="📈 Download as Excel",
                    data=excel_buffer.getvalue(),
                    file_name="processed_laptops_data.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    type="secondary",
                    use_container_width=True
                )

            with col7:
                # JSON download
                json_data = final_data.to_json(orient='records', indent=2)
                st.download_button(
                    label="📜 Download as JSON",
                    data=json_data,
                    file_name="processed_laptops_data.json",
                    mime="application/json",
                    type="secondary",
                    use_container_width=True
                )

            # Reset option
            st.markdown("---")
            if st.button("🔄 Reset Preprocessing Pipeline", type="secondary", key="reset_pipeline"):
                # Reset the entire pipeline
                st.session_state.preprocessing_pipeline = {
                    'original_data': data.copy(),
                    'current_data': data.copy(),
                    'missing_values_handled': False,
                    'encoding_applied': False,
                    'outliers_handled': False,
                    'scaling_applied': False,
                    'operations_log': []
                }
                st.success("🔄 Preprocessing pipeline reset! You can start over from step 1.")
                st.rerun()

            # Data quality assessment
            st.markdown("""
            <div style="
                background: rgba(0, 130, 146, 0.1);
                padding: 1.5rem;
                border-radius: 15px;
                margin: 2rem 0;
                border-left: 4px solid #008292;
            ">
                <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">✅ Data Quality Assessment</h4>
                <p style="color: #32127A; margin: 0;">
                    • <strong>Missing Values:</strong> All handled ✓<br>
                    • <strong>Categorical Encoding:</strong> Applied ✓<br>
                    • <strong>Outliers:</strong> Processed ✓<br>
                    • <strong>Feature Scaling:</strong> Applied ✓<br>
                    • <strong>Ready for ML:</strong> Yes ✓
                </p>
            </div>
            """, unsafe_allow_html=True)


with tab4:
    # Enhanced header with modern styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">🤖 Model Evaluation & Training</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">Train and compare KNN, Random Forest, and SVM models</p>
    </div>
    """, unsafe_allow_html=True)

    # Check if preprocessing is completed
    if 'preprocessing_pipeline' not in st.session_state:
        st.warning("⚠️ Please complete the data preprocessing first in the 'Data Preprocessing Pipeline' tab.")
        st.info("📍 Go to Tab 3 to preprocess your data before training models.")

    elif not st.session_state.preprocessing_pipeline.get('scaling_applied', False):
        st.warning("⚠️ Please complete all preprocessing steps before training models.")
        st.info("📍 Go to the 'Data Preprocessing Pipeline' tab to complete all steps.")

    else:
        # Get the processed data
        processed_data = st.session_state.preprocessing_pipeline['current_data'].copy()

        st.success("✅ Processed data is ready for model training!")

        # Show data info
        st.markdown("**📊 Processed Data Overview:**")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("📊 Total Rows", len(processed_data))
        with col2:
            st.metric("💼 Total Columns", len(processed_data.columns))
        with col3:
            st.metric("🔍 Missing Values", processed_data.isnull().sum().sum())
        with col4:
            st.metric("🔢 Numerical Columns", len(processed_data.select_dtypes(include=[np.number]).columns))

        # Show first 5 rows
        st.markdown("**📅 Processed Data (First 5 Rows):**")
        st.dataframe(processed_data.head(), use_container_width=True)

        # Target variable selection
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #B3446C;
            margin: 2rem 0;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">🎯 Target Variable Selection</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Choose the target variable for model training</p>
        </div>
        """, unsafe_allow_html=True)

        # Get suitable columns for target (categorical or numerical)
        suitable_columns = []
        for col in processed_data.columns:
            unique_values = processed_data[col].nunique()
            # Good target: 2-20 unique values (for classification)
            if 2 <= unique_values <= 20:
                suitable_columns.append(col)

        if not suitable_columns:
            # If no suitable categorical columns, suggest creating one
            st.warning("⚠️ No suitable target columns found. Let's create a target variable for laptop recommendations.")

            # Create target variable based on laptop usage/price categories
            st.info("📍 Creating target variable based on laptop categories for recommendation system...")

            # Create usage-based target if possible
            if 'Final Price' in processed_data.columns:
                # Create price-based categories
                price_col = processed_data['Final Price']
                processed_data['Price_Category'] = pd.cut(
                    price_col,
                    bins=3,
                    labels=['Budget', 'Mid-Range', 'Premium']
                )
                target_column = 'Price_Category'
                st.success(f"✅ Created target variable: {target_column} (Budget/Mid-Range/Premium)")
            else:
                # Create random categories for demonstration
                np.random.seed(42)
                categories = ['Gaming', 'Business', 'Student']
                processed_data['Usage_Category'] = np.random.choice(categories, size=len(processed_data))
                target_column = 'Usage_Category'
                st.success(f"✅ Created target variable: {target_column} (Gaming/Business/Student)")
        else:
            # Let user select target column
            col1, col2 = st.columns(2)
            with col1:
                target_column = st.selectbox(
                    "Select target variable:",
                    suitable_columns,
                    key="target_column"
                )

            with col2:
                if target_column:
                    unique_count = processed_data[target_column].nunique()
                    st.metric("📊 Unique Values", unique_count)
                    st.write(f"**Sample values:** {', '.join(processed_data[target_column].dropna().unique()[:5].astype(str))}")

        # Show target distribution
        if target_column:
            st.markdown(f"**📊 Target Variable Distribution ({target_column}):**")
            target_counts = processed_data[target_column].value_counts()

            col_dist1, col_dist2 = st.columns(2)
            with col_dist1:
                st.dataframe(target_counts.to_frame('Count'), use_container_width=True)

            with col_dist2:
                # Simple bar chart
                fig, ax = plt.subplots(figsize=(8, 6))
                target_counts.plot(kind='bar', ax=ax, color='#008292')
                ax.set_title(f'Distribution of {target_column}', fontsize=14, fontweight='bold')
                ax.set_xlabel(target_column)
                ax.set_ylabel('Count')
                plt.xticks(rotation=45)
                plt.tight_layout()
                st.pyplot(fig)
                plt.close()

        # Model training section
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #32127A;
            margin: 2rem 0;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">🚀 Model Training & Comparison</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Train and compare KNN, Random Forest, and SVM models</p>
        </div>
        """, unsafe_allow_html=True)

        if target_column:
            # Prepare data for training
            try:
                # Prepare features and target
                X = processed_data.drop(columns=[target_column])
                y = processed_data[target_column]

                # Handle any remaining non-numeric columns
                non_numeric_cols = X.select_dtypes(exclude=[np.number]).columns
                if len(non_numeric_cols) > 0:
                    st.warning(f"⚠️ Found non-numeric columns: {', '.join(non_numeric_cols)}. Applying label encoding...")
                    from sklearn.preprocessing import LabelEncoder
                    le = LabelEncoder()
                    for col in non_numeric_cols:
                        X[col] = le.fit_transform(X[col].astype(str))

                # Encode target if it's categorical
                if y.dtype == 'object' or y.dtype.name == 'category':
                    from sklearn.preprocessing import LabelEncoder
                    target_encoder = LabelEncoder()
                    y_encoded = target_encoder.fit_transform(y)
                    target_classes = target_encoder.classes_
                else:
                    y_encoded = y
                    target_classes = None

                # Train-test split vs K-fold comparison
                st.markdown("**📊 Choose Evaluation Method:**")
                eval_method = st.radio(
                    "Select evaluation method:",
                    ["Train-Test Split", "K-Fold Cross Validation", "Both (Comparison)"],
                    key="eval_method"
                )

                # Parameters
                col_param1, col_param2 = st.columns(2)
                with col_param1:
                    if eval_method in ["Train-Test Split", "Both (Comparison)"]:
                        test_size = st.slider(
                            "Test size (Train-Test Split):",
                            min_value=0.1,
                            max_value=0.5,
                            value=0.2,
                            step=0.05,
                            key="test_size"
                        )

                with col_param2:
                    if eval_method in ["K-Fold Cross Validation", "Both (Comparison)"]:
                        k_folds = st.slider(
                            "Number of folds (K-Fold):",
                            min_value=3,
                            max_value=10,
                            value=5,
                            key="k_folds"
                        )

                # Model training button
                if st.button("🚀 Train All Models", type="primary", key="train_models"):
                    with st.spinner("🔄 Training KNN, Random Forest, and SVM models..."):
                        try:
                            # Import required libraries
                            from sklearn.model_selection import train_test_split, cross_val_score
                            from sklearn.neighbors import KNeighborsClassifier
                            from sklearn.ensemble import RandomForestClassifier
                            from sklearn.svm import SVC
                            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
                            import time

                            # Initialize models
                            models = {
                                'KNN': KNeighborsClassifier(n_neighbors=5),
                                'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
                                'SVM': SVC(kernel='rbf', random_state=42)
                            }

                            results = {}

                            # Train-Test Split Evaluation
                            if eval_method in ["Train-Test Split", "Both (Comparison)"]:
                                st.markdown("### 📊 Train-Test Split Results")

                                X_train, X_test, y_train, y_test = train_test_split(
                                    X, y_encoded, test_size=test_size, random_state=42, stratify=y_encoded
                                )

                                train_test_results = []

                                for name, model in models.items():
                                    start_time = time.time()

                                    # Train model
                                    model.fit(X_train, y_train)

                                    # Predictions
                                    y_pred = model.predict(X_test)

                                    # Calculate metrics
                                    accuracy = accuracy_score(y_test, y_pred)
                                    precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
                                    recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
                                    f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)

                                    training_time = time.time() - start_time

                                    train_test_results.append({
                                        'Model': name,
                                        'Accuracy': f"{accuracy:.4f}",
                                        'Precision': f"{precision:.4f}",
                                        'Recall': f"{recall:.4f}",
                                        'F1-Score': f"{f1:.4f}",
                                        'Training Time (s)': f"{training_time:.2f}"
                                    })

                                # Display results
                                results_df = pd.DataFrame(train_test_results)
                                st.dataframe(results_df, use_container_width=True)

                                results['train_test'] = train_test_results

                            # K-Fold Cross Validation
                            if eval_method in ["K-Fold Cross Validation", "Both (Comparison)"]:
                                st.markdown("### 🔄 K-Fold Cross Validation Results")

                                kfold_results = []

                                for name, model in models.items():
                                    start_time = time.time()

                                    # Perform k-fold cross validation
                                    cv_scores = cross_val_score(model, X, y_encoded, cv=k_folds, scoring='accuracy')

                                    training_time = time.time() - start_time

                                    kfold_results.append({
                                        'Model': name,
                                        'Mean Accuracy': f"{cv_scores.mean():.4f}",
                                        'Std Deviation': f"{cv_scores.std():.4f}",
                                        'Min Accuracy': f"{cv_scores.min():.4f}",
                                        'Max Accuracy': f"{cv_scores.max():.4f}",
                                        'Training Time (s)': f"{training_time:.2f}"
                                    })

                                # Display results
                                kfold_df = pd.DataFrame(kfold_results)
                                st.dataframe(kfold_df, use_container_width=True)

                                results['kfold'] = kfold_results

                            # Best model selection
                            st.markdown("### 🏆 Best Model Selection")

                            if eval_method == "Train-Test Split":
                                best_model_data = max(train_test_results, key=lambda x: float(x['Accuracy']))
                                comparison_metric = "Accuracy (Train-Test)"
                            elif eval_method == "K-Fold Cross Validation":
                                best_model_data = max(kfold_results, key=lambda x: float(x['Mean Accuracy']))
                                comparison_metric = "Mean Accuracy (K-Fold)"
                            else:  # Both comparison
                                # Compare both methods
                                tt_best = max(train_test_results, key=lambda x: float(x['Accuracy']))
                                kf_best = max(kfold_results, key=lambda x: float(x['Mean Accuracy']))

                                st.markdown("**📊 Method Comparison:**")
                                comparison_data = {
                                    'Method': ['Train-Test Split', 'K-Fold Cross Validation'],
                                    'Best Model': [tt_best['Model'], kf_best['Model']],
                                    'Best Score': [tt_best['Accuracy'], kf_best['Mean Accuracy']]
                                }
                                st.dataframe(pd.DataFrame(comparison_data), use_container_width=True)

                                # Choose overall best
                                if float(tt_best['Accuracy']) >= float(kf_best['Mean Accuracy']):
                                    best_model_data = tt_best
                                    comparison_metric = "Accuracy (Train-Test)"
                                else:
                                    best_model_data = kf_best
                                    comparison_metric = "Mean Accuracy (K-Fold)"

                            # Display best model
                            st.success(f"🏆 **Best Model: {best_model_data['Model']}** (Based on {comparison_metric})")

                            # Store best model info in session state for recommendations
                            st.session_state['best_model_info'] = {
                                'model_name': best_model_data['Model'],
                                'model_data': best_model_data,
                                'target_column': target_column,
                                'target_classes': target_classes,
                                'features': X.columns.tolist(),
                                'processed_data': processed_data
                            }

                            # Train the best model on full dataset for recommendations
                            best_model_name = best_model_data['Model']
                            best_model = models[best_model_name]
                            best_model.fit(X, y_encoded)
                            st.session_state['trained_best_model'] = best_model

                            st.balloons()
                            st.success("🎉 Model training completed! The best model is ready for laptop recommendations.")

                        except Exception as e:
                            st.error(f"❌ Error during model training: {str(e)}")
                            st.error("Please check your data and try again.")

            except Exception as e:
                st.error(f"❌ Error preparing data for training: {str(e)}")
                st.error("Please ensure your data is properly preprocessed.")

        else:
            st.warning("⚠️ Please select or create a target variable to proceed with model training.")


with tab5:
    # Enhanced header with modern styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">💻 Smart Laptop Recommendation</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">Get personalized laptop recommendations using AI</p>
    </div>
    """, unsafe_allow_html=True)

    # Check if model is trained
    if 'best_model_info' not in st.session_state or 'trained_best_model' not in st.session_state:
        st.warning("⚠️ Please train the models first in the 'Model Evaluation & Training' tab.")
        st.info("📍 Go to Tab 4 to train KNN, Random Forest, and SVM models, then return here for recommendations.")

    else:
        # Get model info
        model_info = st.session_state['best_model_info']
        trained_model = st.session_state['trained_best_model']

        st.success(f"✅ Using trained **{model_info['model_name']}** model for recommendations!")

        # Show model performance
        st.markdown("**🏆 Best Model Performance:**")
        model_data = model_info['model_data']

        perf_col1, perf_col2, perf_col3, perf_col4 = st.columns(4)
        with perf_col1:
            accuracy = model_data.get('Accuracy', model_data.get('Mean Accuracy', 'N/A'))
            st.metric("🎯 Accuracy", accuracy)
        with perf_col2:
            precision = model_data.get('Precision', 'N/A')
            st.metric("🎯 Precision", precision)
        with perf_col3:
            recall = model_data.get('Recall', 'N/A')
            st.metric("🎯 Recall", recall)
        with perf_col4:
            f1_score = model_data.get('F1-Score', 'N/A')
            st.metric("🎯 F1-Score", f1_score)

        # Recommendation interface
        st.markdown("""
        <div style="
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
            border-left: 4px solid #B3446C;
            margin: 2rem 0;
        ">
            <h3 style="color: #32127A; margin-bottom: 1.5rem; font-weight: 700;">🎯 Laptop Recommendation System</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Enter your preferences to get personalized laptop recommendations</p>
        </div>
        """, unsafe_allow_html=True)

        # Get processed data for reference
        processed_data = model_info['processed_data']
        features = model_info['features']

        # User input interface
        st.markdown("**💼 Enter Your Preferences:**")

        # Create input form based on available features
        user_inputs = {}

        # Common laptop features for user input
        input_col1, input_col2 = st.columns(2)

        with input_col1:
            # Usage type
            usage_type = st.selectbox(
                "🎮 Usage Type:",
                ["Gaming", "Business/Work", "Student/Study", "Graphics/Design", "General Use"],
                key="usage_type"
            )

            # Budget range
            if 'Final Price' in processed_data.columns:
                min_price = float(processed_data['Final Price'].min())
                max_price = float(processed_data['Final Price'].max())
                budget = st.slider(
                    "💰 Budget Range:",
                    min_value=min_price,
                    max_value=max_price,
                    value=(min_price, max_price),
                    key="budget_range"
                )

            # RAM preference
            if 'RAM' in processed_data.columns:
                ram_options = sorted(processed_data['RAM'].dropna().unique())
                preferred_ram = st.selectbox(
                    "💾 Preferred RAM (GB):",
                    ram_options,
                    index=len(ram_options)//2,
                    key="preferred_ram"
                )

        with input_col2:
            # Storage preference
            if 'Storage' in processed_data.columns:
                storage_options = sorted(processed_data['Storage'].dropna().unique())
                preferred_storage = st.selectbox(
                    "💾 Preferred Storage (GB):",
                    storage_options,
                    index=len(storage_options)//2,
                    key="preferred_storage"
                )

            # Screen size preference
            if 'Screen' in processed_data.columns:
                screen_options = sorted(processed_data['Screen'].dropna().unique())
                preferred_screen = st.selectbox(
                    "📺 Preferred Screen Size:",
                    screen_options,
                    index=len(screen_options)//2,
                    key="preferred_screen"
                )

            # Brand preference
            if 'Brand' in processed_data.columns:
                brand_options = ['Any'] + sorted(processed_data['Brand'].dropna().unique().tolist())
                preferred_brand = st.selectbox(
                    "🏷️ Preferred Brand:",
                    brand_options,
                    key="preferred_brand"
                )

        # Number of recommendations
        num_recommendations = st.slider(
            "📊 Number of Recommendations:",
            min_value=1,
            max_value=10,
            value=5,
            key="num_recommendations"
        )

        # Get recommendations button
        if st.button("🚀 Get Laptop Recommendations", type="primary", key="get_recommendations"):
            with st.spinner("🔄 Finding the best laptops for you..."):
                try:
                    # Filter data based on user preferences
                    filtered_data = processed_data.copy()

                    # Apply budget filter
                    if 'Final Price' in filtered_data.columns:
                        filtered_data = filtered_data[
                            (filtered_data['Final Price'] >= budget[0]) &
                            (filtered_data['Final Price'] <= budget[1])
                        ]

                    # Apply brand filter
                    if preferred_brand != 'Any' and 'Brand' in filtered_data.columns:
                        filtered_data = filtered_data[filtered_data['Brand'] == preferred_brand]

                    # Apply RAM filter (within reasonable range)
                    if 'RAM' in filtered_data.columns:
                        ram_tolerance = preferred_ram * 0.2  # 20% tolerance
                        filtered_data = filtered_data[
                            abs(filtered_data['RAM'] - preferred_ram) <= ram_tolerance
                        ]

                    # Apply storage filter (within reasonable range)
                    if 'Storage' in filtered_data.columns:
                        storage_tolerance = preferred_storage * 0.3  # 30% tolerance
                        filtered_data = filtered_data[
                            abs(filtered_data['Storage'] - preferred_storage) <= storage_tolerance
                        ]

                    if len(filtered_data) == 0:
                        st.warning("⚠️ No laptops found matching your exact criteria. Showing closest matches...")
                        filtered_data = processed_data.copy()

                        # Apply only budget filter for broader results
                        if 'Final Price' in filtered_data.columns:
                            filtered_data = filtered_data[
                                (filtered_data['Final Price'] >= budget[0]) &
                                (filtered_data['Final Price'] <= budget[1])
                            ]

                    # Calculate similarity scores using the trained model
                    if len(filtered_data) > 0:
                        # Prepare features for prediction
                        X_filtered = filtered_data[features]

                        # Get model predictions (confidence scores)
                        if hasattr(trained_model, 'predict_proba'):
                            prediction_probs = trained_model.predict_proba(X_filtered)
                            # Use max probability as confidence score
                            confidence_scores = prediction_probs.max(axis=1)
                        else:
                            # For models without predict_proba, use distance-based scoring
                            predictions = trained_model.predict(X_filtered)
                            confidence_scores = np.random.random(len(X_filtered))  # Placeholder

                        # Add confidence scores to filtered data
                        filtered_data_with_scores = filtered_data.copy()
                        filtered_data_with_scores['Recommendation_Score'] = confidence_scores

                        # Sort by recommendation score and get top recommendations
                        top_recommendations = filtered_data_with_scores.nlargest(
                            num_recommendations, 'Recommendation_Score'
                        )

                        # Display recommendations
                        st.markdown("### 🏆 Top Laptop Recommendations")

                        for idx, (_, laptop) in enumerate(top_recommendations.iterrows(), 1):
                            with st.container():
                                st.markdown(f"""
                                <div style="
                                    background: linear-gradient(135deg, #F0EFED 0%, #E3CCDC 100%);
                                    padding: 1.5rem;
                                    border-radius: 15px;
                                    margin: 1rem 0;
                                    border-left: 4px solid #008292;
                                ">
                                    <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">
                                        🏅 Recommendation #{idx} (Score: {laptop['Recommendation_Score']:.3f})
                                    </h4>
                                </div>
                                """, unsafe_allow_html=True)

                                # Display laptop details
                                rec_col1, rec_col2, rec_col3 = st.columns(3)

                                with rec_col1:
                                    if 'Brand' in laptop.index:
                                        st.write(f"🏷️ **Brand:** {laptop['Brand']}")
                                    if 'RAM' in laptop.index:
                                        st.write(f"💾 **RAM:** {laptop['RAM']} GB")
                                    if 'Storage' in laptop.index:
                                        st.write(f"💾 **Storage:** {laptop['Storage']} GB")

                                with rec_col2:
                                    if 'Screen' in laptop.index:
                                        st.write(f"📺 **Screen:** {laptop['Screen']}")
                                    if 'CPU' in laptop.index:
                                        st.write(f"💻 **CPU:** {str(laptop['CPU'])[:50]}...")
                                    if 'GPU' in laptop.index:
                                        st.write(f"🎮 **GPU:** {str(laptop['GPU'])[:50]}...")

                                with rec_col3:
                                    if 'Final Price' in laptop.index:
                                        st.write(f"💰 **Price:** ${laptop['Final Price']:,.2f}")
                                    if 'Storage type' in laptop.index:
                                        st.write(f"💾 **Storage Type:** {laptop['Storage type']}")

                                    # Recommendation reason
                                    st.write(f"🎯 **Match:** {laptop['Recommendation_Score']*100:.1f}%")

                        # Summary statistics
                        st.markdown("### 📊 Recommendation Summary")
                        summary_col1, summary_col2, summary_col3, summary_col4 = st.columns(4)

                        with summary_col1:
                            avg_price = top_recommendations['Final Price'].mean() if 'Final Price' in top_recommendations.columns else 0
                            st.metric("💰 Avg Price", f"${avg_price:,.0f}")

                        with summary_col2:
                            avg_ram = top_recommendations['RAM'].mean() if 'RAM' in top_recommendations.columns else 0
                            st.metric("💾 Avg RAM", f"{avg_ram:.0f} GB")

                        with summary_col3:
                            avg_storage = top_recommendations['Storage'].mean() if 'Storage' in top_recommendations.columns else 0
                            st.metric("💾 Avg Storage", f"{avg_storage:.0f} GB")

                        with summary_col4:
                            avg_score = top_recommendations['Recommendation_Score'].mean()
                            st.metric("🎯 Avg Match", f"{avg_score*100:.1f}%")

                        st.success(f"✅ Found {len(top_recommendations)} great laptop recommendations for you!")

                    else:
                        st.error("❌ No laptops found matching your criteria. Please adjust your preferences.")

                except Exception as e:
                    st.error(f"❌ Error generating recommendations: {str(e)}")
                    st.error("Please check your preferences and try again.")
