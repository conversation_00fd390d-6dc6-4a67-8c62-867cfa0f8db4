# ==========================
# Visualization Utilities
# ==========================

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from config import COLORS

def setup_plot_style():
    """Set up matplotlib style for consistent plotting."""
    plt.style.use('default')
    
def create_styled_figure(figsize=(10, 8)):
    """Create a figure with consistent styling."""
    fig, ax = plt.subplots(figsize=figsize)
    fig.patch.set_facecolor(COLORS["background"])
    return fig, ax

def style_axes(ax, title="", xlabel="", ylabel=""):
    """Apply consistent styling to axes."""
    ax.set_title(title, fontsize=14, weight='bold', color=COLORS["heading"])
    ax.set_xlabel(xlabel, fontsize=12, weight='bold', color=COLORS["heading"])
    ax.set_ylabel(ylabel, fontsize=12, weight='bold', color=COLORS["heading"])
    ax.grid(True, alpha=0.3, color=COLORS["heading"])
    ax.tick_params(colors=COLORS["heading"])
    
    for spine in ax.spines.values():
        spine.set_color(COLORS["heading"])

def create_donut_chart(data, title="", colors=None):
    """Create a styled donut chart."""
    if colors is None:
        colors = [COLORS["heading"], COLORS["button_primary"], COLORS["accent"], 
                 COLORS["card_background"], COLORS["secondary_background"]]
    
    fig, ax = create_styled_figure()
    
    wedges, texts, autotexts = ax.pie(
        data.values,
        labels=data.index,
        autopct='%1.1f%%',
        colors=colors,
        startangle=90,
        pctdistance=0.85,
        textprops={'fontsize': 11, 'weight': 'bold', 'color': COLORS["heading"]}
    )
    
    # Improve text contrast
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(10)

    for text in texts:
        text.set_color(COLORS["heading"])
        text.set_fontweight('bold')
        text.set_fontsize(10)

    # Create donut effect
    centre_circle = plt.Circle((0,0), 0.70, fc=COLORS["background"])
    fig.gca().add_artist(centre_circle)

    ax.set_title(title, fontsize=16, weight='bold', color=COLORS["heading"], pad=20)
    plt.tight_layout()
    
    return fig

def create_bar_chart(data, title="", xlabel="", ylabel="", horizontal=False, color=None):
    """Create a styled bar chart."""
    if color is None:
        color = COLORS["button_primary"]
    
    fig, ax = create_styled_figure()
    
    if horizontal:
        bars = ax.barh(data.index, data.values, color=color, alpha=0.8, 
                      edgecolor=COLORS["heading"], linewidth=1.5)
        # Add value labels
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + max(data.values) * 0.01, bar.get_y() + bar.get_height()/2,
                   f'{width:.0f}', ha='left', va='center', fontweight='bold', 
                   color=COLORS["heading"])
    else:
        bars = ax.bar(data.index, data.values, color=color, alpha=0.8, 
                     edgecolor=COLORS["heading"], linewidth=1.5)
        # Add value labels
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(data.values) * 0.01,
                   f'{int(height)}', ha='center', va='bottom', fontweight='bold', 
                   color=COLORS["heading"])
    
    style_axes(ax, title, xlabel, ylabel)
    plt.tight_layout()
    
    return fig

def create_histogram(data, title="", xlabel="", bins=30, color=None, kde=True):
    """Create a styled histogram with optional KDE."""
    if color is None:
        color = COLORS["button_primary"]
    
    fig, ax = create_styled_figure()
    
    n, bins, patches = ax.hist(data.dropna(), bins=bins, alpha=0.8, color=color, 
                              edgecolor=COLORS["heading"])
    
    # Add KDE line if requested
    if kde:
        try:
            ax2 = ax.twinx()
            data.dropna().plot.kde(ax=ax2, color=COLORS["accent"], linewidth=2)
            ax2.set_ylabel('Density', color=COLORS["heading"], fontweight='bold')
            ax2.tick_params(colors=COLORS["heading"])
            ax2.spines['right'].set_color(COLORS["heading"])
        except:
            pass
    
    style_axes(ax, title, xlabel, "Frequency")
    plt.tight_layout()
    
    return fig

def create_scatter_plot(x_data, y_data, title="", xlabel="", ylabel="", color_data=None):
    """Create a styled scatter plot."""
    fig, ax = create_styled_figure()
    
    if color_data is not None:
        scatter = ax.scatter(x_data, y_data, c=color_data, cmap='viridis',
                           alpha=0.6, s=60, edgecolors=COLORS["heading"], linewidth=0.5)
        plt.colorbar(scatter, label=ylabel)
    else:
        ax.scatter(x_data, y_data, color=COLORS["button_primary"], alpha=0.6, s=60,
                  edgecolors=COLORS["heading"], linewidth=0.5)
    
    style_axes(ax, title, xlabel, ylabel)
    plt.tight_layout()
    
    return fig

def create_pie_chart(data, title="", colors=None):
    """Create a styled pie chart."""
    if colors is None:
        colors = [COLORS["secondary_background"], COLORS["heading"]]
    
    fig, ax = create_styled_figure()
    
    wedges, texts, autotexts = ax.pie(data.values, labels=data.index,
                                     autopct='%1.1f%%', colors=colors, startangle=90)

    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')

    for text in texts:
        text.set_color(COLORS["heading"])
        text.set_fontweight('bold')

    ax.set_title(title, fontsize=14, weight='bold', color=COLORS["heading"])
    plt.tight_layout()
    
    return fig

def create_box_plot(data, title="", xlabel="", ylabel=""):
    """Create a styled box plot."""
    fig, ax = create_styled_figure()
    
    box_plot = ax.boxplot(data.dropna(), patch_artist=True)
    
    # Style the boxes
    for patch in box_plot['boxes']:
        patch.set_facecolor(COLORS["button_primary"])
        patch.set_alpha(0.7)
    
    for element in ['whiskers', 'fliers', 'medians', 'caps']:
        plt.setp(box_plot[element], color=COLORS["heading"])
    
    style_axes(ax, title, xlabel, ylabel)
    plt.tight_layout()
    
    return fig

def wrap_chart_in_container(chart_html="", border_color=None):
    """Wrap a chart in a styled container."""
    if border_color is None:
        border_color = COLORS["button_primary"]
    
    return f"""
    <div style="
        background: rgba(255, 255, 255, 0.9);
        padding: 1rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
        border-left: 4px solid {border_color};
        margin-bottom: 1rem;
    ">
        {chart_html}
    </div>
    """
