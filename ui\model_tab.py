# ==========================
# Model Evaluation Tab Component
# ==========================

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from config import COLORS
from ui.styles import create_section_header, create_info_card
from core.model_trainer import ModelTrainer
from utils.visualization import create_bar_chart, wrap_chart_in_container

def render_model_tab(data):
    """Render the model evaluation and training tab."""
    
    # Enhanced header
    st.markdown(create_section_header(
        "🤖 Model Evaluation & Training",
        "Train and compare KNN, Random Forest, and SVM models"
    ), unsafe_allow_html=True)

    # Check if preprocessing is completed
    if 'preprocessing_pipeline' not in st.session_state:
        st.warning("⚠️ Please complete the data preprocessing first in the 'Data Cleaning Insights' tab.")
        st.info("📍 Go to Tab 3 to preprocess your data before training models.")
        return

    if not st.session_state.preprocessing_pipeline.get('scaling_applied', False):
        st.warning("⚠️ Please complete all preprocessing steps before training models.")
        st.info("📍 Go to the 'Data Cleaning Insights' tab to complete all steps.")
        return

    # Get the processed data
    processed_data = st.session_state.preprocessing_pipeline['current_data'].copy()

    st.success("✅ Processed data is ready for model training!")

    # Show data info
    render_data_overview(processed_data)

    # Initialize model trainer
    trainer = ModelTrainer()

    # Target variable selection
    render_target_selection(trainer, processed_data)

def render_data_overview(processed_data):
    """Render processed data overview."""
    st.markdown("**📊 Processed Data Overview:**")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("📊 Total Rows", len(processed_data))
    with col2:
        st.metric("💼 Total Columns", len(processed_data.columns))
    with col3:
        st.metric("🔍 Missing Values", processed_data.isnull().sum().sum())
    with col4:
        st.metric("🔢 Numerical Columns", len(processed_data.select_dtypes(include=[np.number]).columns))

    # Show first 5 rows
    st.markdown("**📅 Processed Data (First 5 Rows):**")
    st.dataframe(processed_data.head(), use_container_width=True)

def render_target_selection(trainer, processed_data):
    """Render target variable selection section."""
    st.markdown(create_info_card(
        "🎯 Target Variable Selection",
        "Choose the target variable for model training"
    ), unsafe_allow_html=True)

    # Get suitable columns for target
    suitable_columns = trainer.find_suitable_target_columns(processed_data)

    if not suitable_columns:
        # Create target variable
        st.warning("⚠️ No suitable target columns found. Let's create a target variable for laptop recommendations.")
        st.info("📍 Creating target variable based on laptop categories for recommendation system...")

        processed_data, target_column = trainer.create_target_variable(processed_data)
        st.success(f"✅ Created target variable: {target_column}")
        
        # Update the processed data in session state
        st.session_state.preprocessing_pipeline['current_data'] = processed_data
    else:
        # Let user select target column
        col1, col2 = st.columns(2)
        with col1:
            target_column = st.selectbox(
                "Select target variable:",
                suitable_columns,
                key="target_column"
            )

        with col2:
            if target_column:
                unique_count = processed_data[target_column].nunique()
                st.metric("📊 Unique Values", unique_count)
                sample_values = processed_data[target_column].dropna().unique()[:5]
                st.write(f"**Sample values:** {', '.join(sample_values.astype(str))}")

    # Show target distribution
    if target_column:
        render_target_distribution(processed_data, target_column)
        
        # Model training section
        render_model_training_section(trainer, processed_data, target_column)

def render_target_distribution(processed_data, target_column):
    """Render target variable distribution."""
    st.markdown(f"**📊 Target Variable Distribution ({target_column}):**")
    target_counts = processed_data[target_column].value_counts()

    col_dist1, col_dist2 = st.columns(2)
    with col_dist1:
        st.dataframe(target_counts.to_frame('Count'), use_container_width=True)

    with col_dist2:
        # Create distribution chart
        fig = create_bar_chart(
            target_counts, 
            f'Distribution of {target_column}',
            target_column,
            'Count'
        )
        st.markdown(wrap_chart_in_container(), unsafe_allow_html=True)
        st.pyplot(fig)
        plt.close()

def render_model_training_section(trainer, processed_data, target_column):
    """Render model training and comparison section."""
    st.markdown(create_info_card(
        "🚀 Model Training & Comparison",
        "Train and compare KNN, Random Forest, and SVM models"
    ), unsafe_allow_html=True)

    try:
        # Prepare data for training
        X, y_encoded, target_classes, features = trainer.prepare_data_for_training(
            processed_data, target_column
        )

        # Evaluation method selection
        st.markdown("**📊 Choose Evaluation Method:**")
        eval_method = st.radio(
            "Select evaluation method:",
            ["Train-Test Split", "K-Fold Cross Validation", "Both (Comparison)"],
            key="eval_method"
        )

        # Parameters
        col_param1, col_param2 = st.columns(2)
        with col_param1:
            if eval_method in ["Train-Test Split", "Both (Comparison)"]:
                test_size = st.slider(
                    "Test size (Train-Test Split):",
                    min_value=0.1,
                    max_value=0.5,
                    value=0.2,
                    step=0.05,
                    key="test_size"
                )

        with col_param2:
            if eval_method in ["K-Fold Cross Validation", "Both (Comparison)"]:
                k_folds = st.slider(
                    "Number of folds (K-Fold):",
                    min_value=3,
                    max_value=10,
                    value=5,
                    key="k_folds"
                )

        # Model training button
        if st.button("🚀 Train All Models", type="primary", key="train_models"):
            with st.spinner("🔄 Training KNN, Random Forest, and SVM models..."):
                try:
                    results = {}

                    # Train-Test Split Evaluation
                    if eval_method in ["Train-Test Split", "Both (Comparison)"]:
                        st.markdown("### 📊 Train-Test Split Results")
                        
                        train_test_results = trainer.train_models_train_test_split(X, y_encoded, test_size)
                        results_df = pd.DataFrame(train_test_results)
                        st.dataframe(results_df, use_container_width=True)
                        results['train_test'] = train_test_results

                    # K-Fold Cross Validation
                    if eval_method in ["K-Fold Cross Validation", "Both (Comparison)"]:
                        st.markdown("### 🔄 K-Fold Cross Validation Results")
                        
                        kfold_results = trainer.train_models_cross_validation(X, y_encoded, k_folds)
                        kfold_df = pd.DataFrame(kfold_results)
                        st.dataframe(kfold_df, use_container_width=True)
                        results['kfold'] = kfold_results

                    # Best model selection
                    render_best_model_selection(
                        trainer, results, eval_method, X, y_encoded, 
                        target_column, target_classes, features, processed_data
                    )

                except Exception as e:
                    st.error(f"❌ Error during model training: {str(e)}")
                    st.error("Please check your data and try again.")

    except Exception as e:
        st.error(f"❌ Error preparing data for training: {str(e)}")
        st.error("Please ensure your data is properly preprocessed.")

def render_best_model_selection(trainer, results, eval_method, X, y_encoded, 
                               target_column, target_classes, features, processed_data):
    """Render best model selection and storage."""
    st.markdown("### 🏆 Best Model Selection")

    # Determine best model based on evaluation method
    if eval_method == "Train-Test Split":
        best_model_info = trainer.select_best_model(
            train_test_results=results['train_test'], method="train_test"
        )
    elif eval_method == "K-Fold Cross Validation":
        best_model_info = trainer.select_best_model(
            kfold_results=results['kfold'], method="kfold"
        )
    else:  # Both comparison
        best_model_info = trainer.select_best_model(
            train_test_results=results['train_test'],
            kfold_results=results['kfold'],
            method="both"
        )
        
        # Show method comparison
        st.markdown("**📊 Method Comparison:**")
        tt_best = max(results['train_test'], key=lambda x: float(x['Accuracy']))
        kf_best = max(results['kfold'], key=lambda x: float(x['Mean Accuracy']))
        
        comparison_data = {
            'Method': ['Train-Test Split', 'K-Fold Cross Validation'],
            'Best Model': [tt_best['Model'], kf_best['Model']],
            'Best Score': [tt_best['Accuracy'], kf_best['Mean Accuracy']]
        }
        st.dataframe(pd.DataFrame(comparison_data), use_container_width=True)

    if best_model_info:
        best_model_data = best_model_info['model_data']
        comparison_metric = best_model_info['comparison_metric']
        
        # Display best model
        st.success(f"🏆 **Best Model: {best_model_data['Model']}** (Based on {comparison_metric})")

        # Train the best model on full dataset for recommendations
        best_model_name = best_model_data['Model']
        trained_best_model = trainer.train_best_model_on_full_data(X, y_encoded, best_model_name)

        # Store model info in session state
        model_info = trainer.get_model_info_for_session(
            best_model_data, target_column, target_classes, features, processed_data
        )
        
        st.session_state['best_model_info'] = model_info
        st.session_state['trained_best_model'] = trained_best_model

        # Show feature importance if available
        feature_importance = trainer.get_feature_importance(trained_best_model, features)
        if feature_importance is not None:
            st.markdown("### 📊 Feature Importance")
            
            # Show top 10 features
            top_features = feature_importance.head(10)
            
            col_imp1, col_imp2 = st.columns(2)
            with col_imp1:
                st.dataframe(top_features, use_container_width=True)
            
            with col_imp2:
                # Create feature importance chart
                fig = create_bar_chart(
                    pd.Series(top_features['Importance'].values, index=top_features['Feature']),
                    "Top 10 Feature Importance",
                    "Importance",
                    "Features"
                )
                st.markdown(wrap_chart_in_container(border_color=COLORS["accent"]), unsafe_allow_html=True)
                st.pyplot(fig)
                plt.close()

        st.balloons()
        st.success("🎉 Model training completed! The best model is ready for laptop recommendations.")

def render_model_comparison_charts(results):
    """Render model comparison visualization charts."""
    if 'train_test' in results:
        st.markdown("### 📊 Model Performance Comparison")
        
        # Extract accuracy scores for visualization
        models = [result['Model'] for result in results['train_test']]
        accuracies = [float(result['Accuracy']) for result in results['train_test']]
        
        # Create comparison chart
        accuracy_series = pd.Series(accuracies, index=models)
        fig = create_bar_chart(
            accuracy_series,
            "Model Accuracy Comparison (Train-Test Split)",
            "Accuracy",
            "Models"
        )
        
        st.markdown(wrap_chart_in_container(border_color=COLORS["heading"]), unsafe_allow_html=True)
        st.pyplot(fig)
        plt.close()

    if 'kfold' in results:
        # K-fold comparison
        models = [result['Model'] for result in results['kfold']]
        mean_accuracies = [float(result['Mean Accuracy']) for result in results['kfold']]
        
        accuracy_series = pd.Series(mean_accuracies, index=models)
        fig = create_bar_chart(
            accuracy_series,
            "Model Mean Accuracy Comparison (K-Fold CV)",
            "Mean Accuracy",
            "Models"
        )
        
        st.markdown(wrap_chart_in_container(border_color=COLORS["accent"]), unsafe_allow_html=True)
        st.pyplot(fig)
        plt.close()
