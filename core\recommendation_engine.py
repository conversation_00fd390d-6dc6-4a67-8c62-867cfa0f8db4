# ==========================
# Recommendation Engine
# ==========================

import pandas as pd
import numpy as np
import streamlit as st
from sklearn.preprocessing import LabelEncoder

class RecommendationEngine:
    """Class to handle laptop recommendations using trained ML models."""
    
    def __init__(self, model_info, trained_model):
        """
        Initialize the recommendation engine.
        
        Args:
            model_info (dict): Model information from session state
            trained_model: Trained ML model
        """
        self.model_info = model_info
        self.trained_model = trained_model
        self.processed_data = model_info['processed_data']
        self.features = model_info['features']
        self.target_column = model_info['target_column']
        self.target_classes = model_info['target_classes']
        self.target_encoder = model_info.get('target_encoder')
        self.feature_encoders = model_info.get('feature_encoders', {})
    
    def filter_data_by_preferences(self, user_preferences):
        """
        Filter the dataset based on user preferences.
        
        Args:
            user_preferences (dict): User input preferences
            
        Returns:
            pd.DataFrame: Filtered dataset
        """
        filtered_data = self.processed_data.copy()
        
        # Apply budget filter
        if 'budget_range' in user_preferences and 'Final Price' in filtered_data.columns:
            budget_min, budget_max = user_preferences['budget_range']
            filtered_data = filtered_data[
                (filtered_data['Final Price'] >= budget_min) &
                (filtered_data['Final Price'] <= budget_max)
            ]
        
        # Apply brand filter
        if (user_preferences.get('preferred_brand') != 'Any' and 
            'Brand' in filtered_data.columns):
            filtered_data = filtered_data[
                filtered_data['Brand'] == user_preferences['preferred_brand']
            ]
        
        # Apply RAM filter (within tolerance)
        if 'preferred_ram' in user_preferences and 'RAM' in filtered_data.columns:
            ram_tolerance = user_preferences['preferred_ram'] * 0.2  # 20% tolerance
            filtered_data = filtered_data[
                abs(filtered_data['RAM'] - user_preferences['preferred_ram']) <= ram_tolerance
            ]
        
        # Apply storage filter (within tolerance)
        if 'preferred_storage' in user_preferences and 'Storage' in filtered_data.columns:
            storage_tolerance = user_preferences['preferred_storage'] * 0.3  # 30% tolerance
            filtered_data = filtered_data[
                abs(filtered_data['Storage'] - user_preferences['preferred_storage']) <= storage_tolerance
            ]
        
        # Apply screen size filter (within tolerance)
        if 'preferred_screen' in user_preferences and 'Screen' in filtered_data.columns:
            screen_tolerance = 0.5  # 0.5 inch tolerance
            filtered_data = filtered_data[
                abs(filtered_data['Screen'] - user_preferences['preferred_screen']) <= screen_tolerance
            ]
        
        return filtered_data
    
    def calculate_recommendation_scores(self, filtered_data):
        """
        Calculate recommendation scores using the trained model.
        
        Args:
            filtered_data (pd.DataFrame): Filtered dataset
            
        Returns:
            np.array: Recommendation scores
        """
        if len(filtered_data) == 0:
            return np.array([])
        
        # Prepare features for prediction
        X_filtered = filtered_data[self.features]
        
        # Get model predictions (confidence scores)
        if hasattr(self.trained_model, 'predict_proba'):
            prediction_probs = self.trained_model.predict_proba(X_filtered)
            # Use max probability as confidence score
            confidence_scores = prediction_probs.max(axis=1)
        elif hasattr(self.trained_model, 'decision_function'):
            # For SVM with decision function
            decision_scores = self.trained_model.decision_function(X_filtered)
            if decision_scores.ndim > 1:
                confidence_scores = decision_scores.max(axis=1)
            else:
                confidence_scores = np.abs(decision_scores)
        else:
            # Fallback: use random scores
            confidence_scores = np.random.random(len(X_filtered))
        
        return confidence_scores
    
    def get_recommendations(self, user_preferences, num_recommendations=5):
        """
        Get laptop recommendations based on user preferences.
        
        Args:
            user_preferences (dict): User input preferences
            num_recommendations (int): Number of recommendations to return
            
        Returns:
            pd.DataFrame: Top recommendations with scores
        """
        # Filter data based on preferences
        filtered_data = self.filter_data_by_preferences(user_preferences)
        
        # If no data matches exact criteria, broaden the search
        if len(filtered_data) == 0:
            st.warning("⚠️ No laptops found matching your exact criteria. Showing closest matches...")
            filtered_data = self.processed_data.copy()
            
            # Apply only budget filter for broader results
            if 'budget_range' in user_preferences and 'Final Price' in filtered_data.columns:
                budget_min, budget_max = user_preferences['budget_range']
                filtered_data = filtered_data[
                    (filtered_data['Final Price'] >= budget_min) &
                    (filtered_data['Final Price'] <= budget_max)
                ]
        
        if len(filtered_data) == 0:
            return pd.DataFrame()
        
        # Calculate recommendation scores
        confidence_scores = self.calculate_recommendation_scores(filtered_data)
        
        # Add scores to filtered data
        filtered_data_with_scores = filtered_data.copy()
        filtered_data_with_scores['Recommendation_Score'] = confidence_scores
        
        # Sort by recommendation score and get top recommendations
        top_recommendations = filtered_data_with_scores.nlargest(
            num_recommendations, 'Recommendation_Score'
        )
        
        return top_recommendations
    
    def create_user_profile_vector(self, user_preferences):
        """
        Create a user profile vector based on preferences.
        
        Args:
            user_preferences (dict): User input preferences
            
        Returns:
            dict: User profile for similarity calculation
        """
        profile = {}
        
        # Map usage type to numerical values
        usage_mapping = {
            "Gaming": 1,
            "Business/Work": 2,
            "Student/Study": 3,
            "Graphics/Design": 4,
            "General Use": 5
        }
        
        profile['usage_score'] = usage_mapping.get(user_preferences.get('usage_type', 'General Use'), 5)
        
        # Add other preferences
        if 'preferred_ram' in user_preferences:
            profile['ram_preference'] = user_preferences['preferred_ram']
        
        if 'preferred_storage' in user_preferences:
            profile['storage_preference'] = user_preferences['preferred_storage']
        
        if 'preferred_screen' in user_preferences:
            profile['screen_preference'] = user_preferences['preferred_screen']
        
        if 'budget_range' in user_preferences:
            budget_min, budget_max = user_preferences['budget_range']
            profile['budget_preference'] = (budget_min + budget_max) / 2
        
        return profile
    
    def calculate_similarity_scores(self, filtered_data, user_profile):
        """
        Calculate similarity scores between user profile and laptops.
        
        Args:
            filtered_data (pd.DataFrame): Filtered dataset
            user_profile (dict): User profile vector
            
        Returns:
            np.array: Similarity scores
        """
        similarity_scores = []
        
        for _, laptop in filtered_data.iterrows():
            score = 0
            factors = 0
            
            # RAM similarity
            if 'ram_preference' in user_profile and 'RAM' in laptop.index:
                ram_diff = abs(laptop['RAM'] - user_profile['ram_preference'])
                ram_score = max(0, 1 - (ram_diff / user_profile['ram_preference']))
                score += ram_score
                factors += 1
            
            # Storage similarity
            if 'storage_preference' in user_profile and 'Storage' in laptop.index:
                storage_diff = abs(laptop['Storage'] - user_profile['storage_preference'])
                storage_score = max(0, 1 - (storage_diff / user_profile['storage_preference']))
                score += storage_score
                factors += 1
            
            # Screen similarity
            if 'screen_preference' in user_profile and 'Screen' in laptop.index:
                screen_diff = abs(laptop['Screen'] - user_profile['screen_preference'])
                screen_score = max(0, 1 - (screen_diff / user_profile['screen_preference']))
                score += screen_score
                factors += 1
            
            # Budget similarity
            if 'budget_preference' in user_profile and 'Final Price' in laptop.index:
                budget_diff = abs(laptop['Final Price'] - user_profile['budget_preference'])
                budget_score = max(0, 1 - (budget_diff / user_profile['budget_preference']))
                score += budget_score
                factors += 1
            
            # Average similarity score
            final_score = score / factors if factors > 0 else 0
            similarity_scores.append(final_score)
        
        return np.array(similarity_scores)
    
    def get_hybrid_recommendations(self, user_preferences, num_recommendations=5):
        """
        Get recommendations using hybrid approach (ML model + similarity).
        
        Args:
            user_preferences (dict): User input preferences
            num_recommendations (int): Number of recommendations to return
            
        Returns:
            pd.DataFrame: Top recommendations with hybrid scores
        """
        # Filter data based on preferences
        filtered_data = self.filter_data_by_preferences(user_preferences)
        
        if len(filtered_data) == 0:
            # Broaden search if no exact matches
            filtered_data = self.processed_data.copy()
            if 'budget_range' in user_preferences and 'Final Price' in filtered_data.columns:
                budget_min, budget_max = user_preferences['budget_range']
                filtered_data = filtered_data[
                    (filtered_data['Final Price'] >= budget_min) &
                    (filtered_data['Final Price'] <= budget_max)
                ]
        
        if len(filtered_data) == 0:
            return pd.DataFrame()
        
        # Get ML model scores
        ml_scores = self.calculate_recommendation_scores(filtered_data)
        
        # Get similarity scores
        user_profile = self.create_user_profile_vector(user_preferences)
        similarity_scores = self.calculate_similarity_scores(filtered_data, user_profile)
        
        # Combine scores (weighted average)
        ml_weight = 0.6
        similarity_weight = 0.4
        
        if len(ml_scores) > 0 and len(similarity_scores) > 0:
            # Normalize scores to 0-1 range
            ml_scores_norm = (ml_scores - ml_scores.min()) / (ml_scores.max() - ml_scores.min() + 1e-8)
            similarity_scores_norm = similarity_scores
            
            hybrid_scores = (ml_weight * ml_scores_norm + 
                           similarity_weight * similarity_scores_norm)
        else:
            hybrid_scores = ml_scores if len(ml_scores) > 0 else similarity_scores
        
        # Add scores to filtered data
        filtered_data_with_scores = filtered_data.copy()
        filtered_data_with_scores['Recommendation_Score'] = hybrid_scores
        
        # Sort by hybrid score and get top recommendations
        top_recommendations = filtered_data_with_scores.nlargest(
            num_recommendations, 'Recommendation_Score'
        )
        
        return top_recommendations
    
    def explain_recommendation(self, laptop_row, user_preferences):
        """
        Generate explanation for why a laptop was recommended.
        
        Args:
            laptop_row (pd.Series): Laptop data
            user_preferences (dict): User preferences
            
        Returns:
            list: List of explanation points
        """
        explanations = []
        
        # Budget match
        if 'budget_range' in user_preferences and 'Final Price' in laptop_row.index:
            budget_min, budget_max = user_preferences['budget_range']
            if budget_min <= laptop_row['Final Price'] <= budget_max:
                explanations.append(f"💰 Within your budget range (${laptop_row['Final Price']:.0f})")
        
        # RAM match
        if 'preferred_ram' in user_preferences and 'RAM' in laptop_row.index:
            ram_diff = abs(laptop_row['RAM'] - user_preferences['preferred_ram'])
            if ram_diff <= user_preferences['preferred_ram'] * 0.2:
                explanations.append(f"🧠 RAM matches your preference ({laptop_row['RAM']}GB)")
        
        # Storage match
        if 'preferred_storage' in user_preferences and 'Storage' in laptop_row.index:
            storage_diff = abs(laptop_row['Storage'] - user_preferences['preferred_storage'])
            if storage_diff <= user_preferences['preferred_storage'] * 0.3:
                explanations.append(f"💾 Storage meets your needs ({laptop_row['Storage']}GB)")
        
        # Brand match
        if (user_preferences.get('preferred_brand') != 'Any' and 
            'Brand' in laptop_row.index and
            laptop_row['Brand'] == user_preferences['preferred_brand']):
            explanations.append(f"🏷️ Your preferred brand ({laptop_row['Brand']})")
        
        # Usage type recommendations
        usage_type = user_preferences.get('usage_type', 'General Use')
        if usage_type == "Gaming" and 'RAM' in laptop_row.index and laptop_row['RAM'] >= 16:
            explanations.append("🎮 High RAM suitable for gaming")
        elif usage_type == "Graphics/Design" and 'RAM' in laptop_row.index and laptop_row['RAM'] >= 16:
            explanations.append("🎨 High performance for graphics work")
        elif usage_type == "Student/Study" and 'Final Price' in laptop_row.index:
            explanations.append("📚 Good value for students")
        
        return explanations
