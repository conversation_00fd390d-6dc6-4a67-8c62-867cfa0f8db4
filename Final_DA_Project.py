# ==========================
# Import Libraries
# ==========================
import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.metrics import precision_score, recall_score, f1_score, classification_report, accuracy_score, roc_curve, auc
from sklearn.model_selection import train_test_split
from scipy.stats import zscore


# ==========================
# Streamlit Page Configuration
# ==========================
st.set_page_config(page_title="Laptop Recommendation System", 
                   page_icon="💻", 
                   layout="wide")

# ==========================
# Load Dataset
# ==========================
@st.cache_data
def load_data():
    try:
        data = pd.read_csv("laptops.csv")
        

        if 'Final Price' not in data.columns:
            if 'Price' in data.columns:
                data.rename(columns={'Price': 'Final Price'}, inplace=True)
            else:
                data['Final Price'] = np.nan

        numeric_cols = data.select_dtypes(include=np.number).columns
        for col in numeric_cols:
            data[col].fillna(data[col].median(), inplace=True)
        
        categorical_cols = data.select_dtypes(exclude=np.number).columns
        for col in categorical_cols:
            data[col].fillna(data[col].mode()[0], inplace=True)
        
        return data
    except FileNotFoundError:
        st.error("Error: File 'laptops.csv' not found.")
        return pd.DataFrame()

data = load_data()

if data.empty:
    st.stop()

# ==========================
# Helper Functions
# ==========================
def handle_missing_values(data):
    if 'Storage type' in data.columns:
        data['Storage type'] = data['Storage type'].fillna(data['Storage type'].mode()[0])
    
    if 'Screen' in data.columns:
        simple_imputer = SimpleImputer(strategy='mean')  
        data['Screen'] = simple_imputer.fit_transform(data[['Screen']])
    
    if 'GPU' in data.columns:
        data['GPU'] = data['GPU'].fillna(data['GPU'].mode()[0])
    return data

def detect_outliers(df, columns, method='zscore', threshold=3):
    outliers = pd.DataFrame()
    
    for column in columns:
        if column in df.columns:
            if method == 'zscore':
                z_scores = zscore(df[column].dropna())
                abs_z_scores = np.abs(z_scores)
                outlier_mask = abs_z_scores > threshold
                
            elif method == 'iqr':
                Q1 = df[column].quantile(0.25)
                Q3 = df[column].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outlier_mask = (df[column] < lower_bound) | (df[column] > upper_bound)
            
            column_outliers = df[outlier_mask]
            outliers = pd.concat([outliers, column_outliers])
    
    return outliers.drop_duplicates()

def clean_data(df):
    df_clean = df.copy()
    
    # Only drop columns if they exist
    columns_to_drop = [col for col in ['CPU', 'GPU'] if col in df_clean.columns]
    if columns_to_drop:
        df_clean = df_clean.drop(columns=columns_to_drop)
    
    df_clean = df_clean.drop_duplicates()
    
    feature_columns = [col for col in ['RAM', 'Storage', 'Final Price', 'Touch'] if col in df_clean.columns]
    df_clean = df_clean.drop_duplicates(subset=feature_columns)
    
    return df_clean

def apply_label_encoding(data):
    label_encoder = LabelEncoder()
    string_columns = data.select_dtypes(include=['object']).columns

    for col in string_columns:
        data[col] = label_encoder.fit_transform(data[col].astype(str))

    if 'Laptop' in data.columns:
        data['Laptop'] = label_encoder.fit_transform(data['Laptop'].astype(str))
    
    return data

def scale_columns(data, cols_to_scale):
    existing_cols = [col for col in cols_to_scale if col in data.columns]
    
    if existing_cols:
        scaler = StandardScaler()
        data[existing_cols] = scaler.fit_transform(data[existing_cols])
    
    return data

def evaluate_model(data, target_column='Laptop', test_size=0.2, n_neighbors=5):
    if target_column not in data.columns:
        return {"error": f"Target column '{target_column}' not found in data"}

    X = data.drop(columns=[target_column])
    y = data[target_column]

    label_encoder = LabelEncoder()
    for col in X.columns:
        if X[col].dtype == 'object':
            X[col] = label_encoder.fit_transform(X[col])

    if y.dtype in ['float64', 'int64']:
        y = pd.qcut(y, q=4, labels=False) 
    elif y.dtype == 'object':
        y = label_encoder.fit_transform(y)
   
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=42)

    model = KNeighborsClassifier(n_neighbors=n_neighbors)
    model.fit(X_train, y_train)

    y_pred = model.predict(X_test)
    y_score = None
    roc_data = None

    # ROC فقط للمشاكل الثنائية
    if len(np.unique(y_test)) == 2:
        if hasattr(model, "predict_proba"):
            y_score = model.predict_proba(X_test)[:, 1]
            fpr, tpr, thresholds = roc_curve(y_test, y_score)
            roc_auc = auc(fpr, tpr)
            roc_data = {"fpr": fpr, "tpr": tpr, "roc_auc": roc_auc}
    
    accuracy = accuracy_score(y_test, y_pred)  
    precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
    recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
    f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)

    report = classification_report(y_test, y_pred, output_dict=True)
    
    return {
        'Accuracy': accuracy,   
        'Precision': precision,
        'Recall': recall,
        'F1-Score': f1,
        'report': report,
        'roc_data': roc_data
    }

# ==========================
# Recommendation Model Logic
# ==========================
def build_recommendation_model(df):

    feature_cols = []
    if 'RAM' in df.columns: feature_cols.append('RAM')
    if 'Storage' in df.columns: feature_cols.append('Storage')
    if 'Final Price' in df.columns: feature_cols.append('Final Price')
    if 'Touch' in df.columns: feature_cols.append('Touch')
    if 'Screen' in df.columns: feature_cols.append('Screen')
    if 'Brand' in df.columns: feature_cols.append('Brand')
    
    if not feature_cols:
        return None, None

    numeric_features = df[feature_cols].select_dtypes(include=np.number).columns.tolist()
    categorical_features = df[feature_cols].select_dtypes(exclude=np.number).columns.tolist()

    numeric_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='median')),
        ('scaler', StandardScaler())])
    
    categorical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='most_frequent')),
        ('onehot', OneHotEncoder(handle_unknown='ignore'))])
    
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', numeric_transformer, numeric_features),
            ('cat', categorical_transformer, categorical_features)])

    target_col = 'Laptop'

    model = Pipeline(steps=[('preprocessor', preprocessor),
                          ('model', KNeighborsClassifier(n_neighbors=5))])
    
    model.fit(df[feature_cols], df[target_col])
    
    return model, feature_cols

recommendation_model, feature_cols = build_recommendation_model(data)

# ==========================
# Main Application Logic
# ==========================
# Sidebar for navigation
st.sidebar.title("Navigation")
options = st.sidebar.radio("Select a page:", 
                          ["Data Visualization", 
                           "Data Cleaning Insights", 
                           "Model Evaluation", 
                           "Laptop Recommendation"])

# Main content
if options == "Data Visualization":
    st.header("Data Visualization")
    
    tab1, tab2, tab3, tab4 = st.tabs(["Brand Analysis", "Price Analysis", 
                                     "Feature Distribution", "Correlations"])
    
    with tab1:
        st.subheader("Brand Distribution Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if 'Brand' in data.columns:
                top_brands = data['Brand'].value_counts().head()
                fig, ax = plt.subplots(figsize=(8, 8))
                ax.pie(
                    top_brands,
                    labels=top_brands.index,
                    autopct='%1.1f%%',
                    radius=1.2,
                    explode=(0.05, 0.05, 0.1, 0.05, 0.05),
                    shadow=True,
                    textprops={'color': 'black', 'weight': 'bold', 'fontsize': 12.5}
                )
                ax.set_title("Top 5 Brands Distribution", weight='bold')
                st.pyplot(fig)
            else:
                st.warning("Brand column not found in data")
        
        with col2:
            if 'Brand' in data.columns:
                fig, ax = plt.subplots(figsize=(10, 6))
                data["Brand"].value_counts().plot(kind="bar", ax=ax)
                ax.set_title("Laptop Count by Brand", fontsize=14, weight='bold')
                ax.grid(True)
                st.pyplot(fig)
            else:
                st.warning("Brand column not found in data")
    
    with tab2:
        st.subheader("Price Analysis")
        
        if all(col in data.columns for col in ['CPU', 'Final Price']):
            fig, ax = plt.subplots(figsize=(18, 8))
            sns.barplot(data=data, x='CPU', y='Final Price', estimator='mean', ax=ax)
            ax.set_title('Average Final Price by CPU Type', fontsize=14, weight='bold')
            ax.set_xlabel('CPU Type', fontsize=12)
            ax.set_ylabel('Average Final Price', fontsize=12)
            plt.xticks(rotation=45, ha='right')
            st.pyplot(fig)
        else:
            st.warning("Required columns (CPU and/or Final Price) not found in data")
        
        if all(col in data.columns for col in ['Storage', 'Final Price']):
            fig, ax = plt.subplots(figsize=(10, 6))
            data.plot(kind='scatter', x='Storage', y='Final Price', ax=ax)
            ax.set_title('Storage vs Final Price')
            ax.grid(True)
            st.pyplot(fig)
        else:
            st.warning("Required columns (Storage and/or Final Price) not found in data")
    
    with tab3:
        st.subheader("Feature Distribution")
        
        if all(col in data.columns for col in ['Touch', 'Brand']):
            fig, ax = plt.subplots(figsize=(10, 7))
            sns.countplot(y="Touch", hue="Brand", data=data, 
                         order=data["Touch"].value_counts().iloc[:5].index, ax=ax)
            ax.set_title("Touchscreen Laptops by Brand", fontsize=14, weight='bold')
            ax.grid(True)
            st.pyplot(fig)
        else:
            st.warning("Required columns (Touch and/or Brand) not found in data")
        
        st.subheader("Numerical Feature Distributions")
        num_features = data.select_dtypes(include=['int', 'float']).columns
        if len(num_features) > 0:
            for i in range(0, len(num_features), 2):
                cols = st.columns(2)
                for j in range(2):
                    if i+j < len(num_features):
                        with cols[j]:
                            fig, ax = plt.subplots(figsize=(8, 4))
                            sns.histplot(data, x=num_features[i+j], kde=True, ax=ax)
                            ax.set_title(f'Distribution of {num_features[i+j]}')
                            st.pyplot(fig)
        else:
            st.warning("No numerical features found in data")
    
    with tab4:
        st.subheader("Feature Correlations")
        
        numeric_data = data.select_dtypes(include=['number'])
        if len(numeric_data.columns) > 1:
            fig, ax = plt.subplots(figsize=(12, 8))
            corr = numeric_data.corr()
            sns.heatmap(corr, annot=True, square=True, cmap='coolwarm', ax=ax)
            ax.set_title("Correlation Matrix", fontsize=14, weight='bold')
            st.pyplot(fig)
        else:
            st.warning("Not enough numerical features for correlation matrix")
        
        if 'Brand' in data.columns and len(numeric_data.columns) > 1:
            st.subheader("Pairplot by Brand")
            st.write("This may take a moment to load...")
            fig = sns.pairplot(data, hue='Brand')
            st.pyplot(fig)
        else:
            st.warning("Brand column or insufficient data for pairplot")

elif options == "Data Cleaning Insights":
    st.header("Data Cleaning Process Insights")
    
    st.subheader("Missing Value Handling")
    st.write("""
    - **Storage type**: Filled with mode (most frequent value)
    - **Screen**: Filled with mean value using SimpleImputer
    - **GPU**: Filled with mode (most frequent value)
    """)
    
    st.subheader("Outlier Detection")
    st.write("""
    Outliers were detected using two methods:
    - Z-score method (default threshold = 3)
    - IQR method (1.5*IQR range)
    """)
    
    num_features = data.select_dtypes(include=['int', 'float']).columns.tolist()
    if num_features:
        selected_feature = st.selectbox("Select feature to view outliers:", num_features)
        method = st.radio("Select outlier detection method:", ['zscore', 'iqr'])
        
        if st.button("Detect Outliers"):
            outliers = detect_outliers(data, [selected_feature], method=method)
            st.write(f"Found {len(outliers)} outliers for {selected_feature}:")
            st.dataframe(outliers)
    else:
        st.warning("No numerical features available for outlier detection")
    
    st.subheader("Duplicate Handling")
    st.write("""
    - Removed exact duplicate rows
    - Removed duplicates based on key features: RAM, Storage, Final Price, Touch
    """)

elif options == "Model Evaluation":
    st.header("Model Evaluation")
    
    eval_tab1, eval_tab2 = st.tabs(["Model Metrics", "Classification Report"])
    
    with eval_tab1:
        st.write("""
        We're using a K-Nearest Neighbors classifier to predict laptop brands based on features.
        """)
        
        # Parameters
        col1, col2 = st.columns(2)
        with col1:
            test_size = st.slider("Test size ratio:", 0.1, 0.5, 0.2, 0.05)
        with col2:
            n_neighbors = st.slider("Number of neighbors (k):", 1, 20, 5)
        
        if st.button("Evaluate Model"):
            # Preprocess data
            processed_data = data.copy()
            processed_data = handle_missing_values(processed_data)
            processed_data = clean_data(processed_data)
            processed_data = apply_label_encoding(processed_data)
            
            numeric_cols = processed_data.select_dtypes(include=['int', 'float']).columns
            processed_data = scale_columns(processed_data, numeric_cols)
            
            if len(processed_data) < 10:
                st.error("Not enough data for model evaluation after preprocessing")
            elif 'Brand' not in processed_data.columns:
                st.error("Brand column not found in processed data")
            else:
                results = evaluate_model(processed_data, test_size=test_size, n_neighbors=n_neighbors)
                
                if 'error' in results:
                    st.error(results['error'])
                else:
                    st.subheader("Model Performance Metrics")
                    col1, col2, col3, col4 = st.columns(4)
                    col1.metric("Accuracy", f"{results['Accuracy']:.2f}")
                    col2.metric("Precision", f"{results['Precision']:.2f}")
                    col3.metric("Recall", f"{results['Recall']:.2f}")
                    col4.metric("F1-Score", f"{results['F1-Score']:.2f}")

    with eval_tab2:
        st.subheader("Detailed Classification Report")
        if st.button("Generate Classification Report"):
            processed_data = data.copy()
            processed_data = handle_missing_values(processed_data)
            processed_data = clean_data(processed_data)
            processed_data = apply_label_encoding(processed_data)
            
            results = evaluate_model(processed_data, test_size=test_size, n_neighbors=n_neighbors)
            
            if 'error' not in results:
                # Create a DataFrame from the classification report
                report_df = pd.DataFrame(results['report']).T
                report_df = report_df.drop('support', axis=1)  # Remove support column
                
                # Format the numbers
                report_df = report_df.round(3)
                
                # Display the report as a styled table
                st.write("Classification Report by Class:")
                st.dataframe(report_df.style.background_gradient(cmap='Blues'))
                
                # Add visualization
                fig, ax = plt.subplots(figsize=(10, 6))
                report_df[['precision', 'recall', 'f1-score']].plot(kind='bar', ax=ax)
                plt.title('Classification Metrics by Class')
                plt.xticks(rotation=45)
                plt.tight_layout()
                st.pyplot(fig)
            else:
                st.error(results['error'])

elif options == "Laptop Recommendation":
    st.title("Laptop Recommendation")
    st.write("Use the form below to find your perfect laptop based on your preferences.")

    with st.form("user_preferences"):
        st.subheader("Your Laptop Preferences")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if 'Final Price' in data.columns:
                min_price, max_price = int(data['Final Price'].min()), int(data['Final Price'].max())
                price_range = st.slider("Budget Range ($)", 
                                      min_price, max_price, 
                                      (min_price, int(data['Final Price'].median())))
            
            if 'RAM' in data.columns:
                ram_options = sorted(data['RAM'].unique())
                ram = st.selectbox("RAM (GB)", ram_options, index=len(ram_options)//2)
            
            if 'Storage' in data.columns:
                storage_options = sorted(data['Storage'].unique())
                storage = st.selectbox("Storage (GB)", storage_options, index=len(storage_options)//2)
        
        with col2:
            if 'Screen' in data.columns:
                screen_options = sorted(data['Screen'].unique())
                screen = st.selectbox("Screen Size (inches)", screen_options, index=len(screen_options)//2)
            

            if 'Touch' in data.columns:
                touch = st.radio("Touchscreen", 
                                ["Yes", "No", "Doesn't matter"], 
                                index=2)
            
            if 'Brand' in data.columns:
                brand_options = data['Brand'].unique()
                preferred_brands = st.multiselect("Preferred Brands (optional)", brand_options)
        
        submit_button = st.form_submit_button("Find My Laptop")

    if submit_button and recommendation_model is not None:
        st.subheader("Recommended Laptops For You")
        

        query = pd.DataFrame(columns=feature_cols)
        

        for col in feature_cols:
            if col == 'Final Price':
                query[col] = [price_range[1]] 
            elif col == 'RAM':
                query[col] = [ram]
            elif col == 'Storage':
                query[col] = [storage]
            elif col == 'Screen':
                query[col] = [screen]
            elif col == 'Touch':
                query[col] = [1 if touch == "Yes" else (0 if touch == "No" else 2)]
            elif col == 'Brand':
                query[col] = [preferred_brands[0] if preferred_brands else 'Unknown']

        distances, indices = recommendation_model.named_steps['model'].kneighbors(
            recommendation_model.named_steps['preprocessor'].transform(query))

        recommended_laptops = data.iloc[indices[0]]

        if preferred_brands and 'Brand' in recommended_laptops.columns:
            recommended_laptops = recommended_laptops[
                recommended_laptops['Brand'].isin(preferred_brands)]

        if len(recommended_laptops) == 0 and 'Brand' in data.columns:
            recommended_laptops = data.iloc[indices[0]]

        if len(recommended_laptops) > 0:
            st.success(f"Found {len(recommended_laptops)} matching laptops!")

            if 'Final Price' in recommended_laptops.columns:
                recommended_laptops = recommended_laptops.sort_values('Final Price')

            for idx, (_, laptop) in enumerate(recommended_laptops.head(5).iterrows(), 1):
                with st.expander(f"Recommendation #{idx}: {laptop.get('Brand', 'Unknown')} - ${laptop.get('Final Price', 'N/A')}", expanded=True):
                    cols = st.columns([1, 3])
                    
                    with cols[0]:
                        st.image("https://via.placeholder.com/200x150?text=Laptop+Image", 
                                width=200, 
                                caption=f"{laptop.get('Brand', '')} Laptop")
                    
                    with cols[1]:
                        st.markdown(f"**Model:** {laptop.get('Laptop', 'Unknown')}")
                        
                        spec_cols = st.columns(2)
                        
                        with spec_cols[0]:
                            if 'CPU' in laptop: st.markdown(f"**Processor:** {laptop['CPU']}")
                            if 'RAM' in laptop: st.markdown(f"**RAM:** {laptop['RAM']}GB")
                        
                        with spec_cols[1]:
                            if 'Storage' in laptop: st.markdown(f"**Storage:** {laptop['Storage']}GB")
                            if 'Screen' in laptop: st.markdown(f"**Screen:** {laptop['Screen']}\"")
                        
                        st.button("View Details", key=f"btn_{idx}")
            
            st.subheader("All Matching Laptops")
            st.dataframe(recommended_laptops)
        else:
            st.warning("No laptops found matching all your criteria. Try adjusting your preferences.")
            
            st.info("Here are some similar laptops you might consider:")
            similar_options = data.sample(5)
            st.dataframe(similar_options)
    elif submit_button and recommendation_model is None:
        st.error("Could not build recommendation model. Please check your data.")