# ==========================
# Model Training and Evaluation
# ==========================

import pandas as pd
import numpy as np
import streamlit as st
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
from config import MODEL_CONFIG
import time

class ModelTrainer:
    """Class to handle model training and evaluation."""
    
    def __init__(self):
        self.models = {
            'KNN': KNeighborsClassifier(**MODEL_CONFIG["models"]["KNN"]),
            'Random Forest': RandomForestClassifier(**MODEL_CONFIG["models"]["Random Forest"]),
            'SVM': SVC(**MODEL_CONFIG["models"]["SVM"])
        }
        self.trained_models = {}
        self.results = {}
        self.target_encoder = None
        self.feature_encoders = {}
    
    def prepare_data_for_training(self, data, target_column):
        """
        Prepare data for model training.
        
        Args:
            data (pd.DataFrame): Processed data
            target_column (str): Target column name
            
        Returns:
            tuple: (X, y, target_classes, feature_names)
        """
        # Prepare features and target
        X = data.drop(columns=[target_column])
        y = data[target_column]
        
        # Handle any remaining non-numeric columns in features
        non_numeric_cols = X.select_dtypes(exclude=[np.number]).columns
        if len(non_numeric_cols) > 0:
            for col in non_numeric_cols:
                le = LabelEncoder()
                X[col] = le.fit_transform(X[col].astype(str))
                self.feature_encoders[col] = le
        
        # Encode target if it's categorical
        if y.dtype == 'object' or y.dtype.name == 'category':
            self.target_encoder = LabelEncoder()
            y_encoded = self.target_encoder.fit_transform(y)
            target_classes = self.target_encoder.classes_
        else:
            y_encoded = y
            target_classes = None
        
        return X, y_encoded, target_classes, X.columns.tolist()
    
    def create_target_variable(self, data):
        """
        Create a suitable target variable for laptop recommendation.
        
        Args:
            data (pd.DataFrame): Input data
            
        Returns:
            tuple: (data_with_target, target_column)
        """
        data_copy = data.copy()
        
        # Create target variable based on laptop categories
        if 'Final Price' in data_copy.columns:
            # Create price-based categories
            price_col = data_copy['Final Price']
            data_copy['Price_Category'] = pd.cut(
                price_col,
                bins=3,
                labels=['Budget', 'Mid-Range', 'Premium']
            )
            target_column = 'Price_Category'
        else:
            # Create random categories for demonstration
            np.random.seed(MODEL_CONFIG["random_state"])
            categories = ['Gaming', 'Business', 'Student']
            data_copy['Usage_Category'] = np.random.choice(categories, size=len(data_copy))
            target_column = 'Usage_Category'
        
        return data_copy, target_column
    
    def find_suitable_target_columns(self, data):
        """
        Find columns suitable for use as target variables.
        
        Args:
            data (pd.DataFrame): Input data
            
        Returns:
            list: List of suitable column names
        """
        suitable_columns = []
        for col in data.columns:
            unique_values = data[col].nunique()
            # Good target: 2-20 unique values (for classification)
            if 2 <= unique_values <= 20:
                suitable_columns.append(col)
        
        return suitable_columns
    
    def train_models_train_test_split(self, X, y, test_size=None):
        """
        Train models using train-test split.
        
        Args:
            X (pd.DataFrame): Features
            y (array): Target
            test_size (float): Test size ratio
            
        Returns:
            list: Training results
        """
        if test_size is None:
            test_size = MODEL_CONFIG["test_size"]
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=MODEL_CONFIG["random_state"], 
            stratify=y
        )
        
        results = []
        
        for name, model in self.models.items():
            start_time = time.time()
            
            # Train model
            model.fit(X_train, y_train)
            
            # Predictions
            y_pred = model.predict(X_test)
            
            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
            
            training_time = time.time() - start_time
            
            # Store trained model
            self.trained_models[name] = model
            
            results.append({
                'Model': name,
                'Accuracy': f"{accuracy:.4f}",
                'Precision': f"{precision:.4f}",
                'Recall': f"{recall:.4f}",
                'F1-Score': f"{f1:.4f}",
                'Training Time (s)': f"{training_time:.2f}"
            })
        
        return results
    
    def train_models_cross_validation(self, X, y, cv_folds=None):
        """
        Train models using cross-validation.
        
        Args:
            X (pd.DataFrame): Features
            y (array): Target
            cv_folds (int): Number of CV folds
            
        Returns:
            list: Cross-validation results
        """
        if cv_folds is None:
            cv_folds = MODEL_CONFIG["cv_folds"]
        
        results = []
        
        for name, model in self.models.items():
            start_time = time.time()
            
            # Perform k-fold cross validation
            cv_scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy')
            
            training_time = time.time() - start_time
            
            results.append({
                'Model': name,
                'Mean Accuracy': f"{cv_scores.mean():.4f}",
                'Std Deviation': f"{cv_scores.std():.4f}",
                'Min Accuracy': f"{cv_scores.min():.4f}",
                'Max Accuracy': f"{cv_scores.max():.4f}",
                'Training Time (s)': f"{training_time:.2f}"
            })
        
        return results
    
    def select_best_model(self, train_test_results=None, kfold_results=None, method="train_test"):
        """
        Select the best performing model.
        
        Args:
            train_test_results (list): Train-test split results
            kfold_results (list): K-fold CV results
            method (str): Evaluation method used
            
        Returns:
            dict: Best model information
        """
        if method == "train_test" and train_test_results:
            best_model_data = max(train_test_results, key=lambda x: float(x['Accuracy']))
            comparison_metric = "Accuracy (Train-Test)"
        elif method == "kfold" and kfold_results:
            best_model_data = max(kfold_results, key=lambda x: float(x['Mean Accuracy']))
            comparison_metric = "Mean Accuracy (K-Fold)"
        elif method == "both" and train_test_results and kfold_results:
            # Compare both methods
            tt_best = max(train_test_results, key=lambda x: float(x['Accuracy']))
            kf_best = max(kfold_results, key=lambda x: float(x['Mean Accuracy']))
            
            # Choose overall best
            if float(tt_best['Accuracy']) >= float(kf_best['Mean Accuracy']):
                best_model_data = tt_best
                comparison_metric = "Accuracy (Train-Test)"
            else:
                best_model_data = kf_best
                comparison_metric = "Mean Accuracy (K-Fold)"
        else:
            return None
        
        return {
            'model_data': best_model_data,
            'comparison_metric': comparison_metric
        }
    
    def train_best_model_on_full_data(self, X, y, best_model_name):
        """
        Train the best model on the full dataset for final use.
        
        Args:
            X (pd.DataFrame): Features
            y (array): Target
            best_model_name (str): Name of the best model
            
        Returns:
            object: Trained model
        """
        best_model = self.models[best_model_name]
        best_model.fit(X, y)
        return best_model
    
    def get_model_info_for_session(self, best_model_data, target_column, target_classes, features, processed_data):
        """
        Prepare model information for session state storage.
        
        Args:
            best_model_data (dict): Best model performance data
            target_column (str): Target column name
            target_classes (array): Target class labels
            features (list): Feature names
            processed_data (pd.DataFrame): Processed dataset
            
        Returns:
            dict: Model information for session state
        """
        return {
            'model_name': best_model_data['Model'],
            'model_data': best_model_data,
            'target_column': target_column,
            'target_classes': target_classes,
            'features': features,
            'processed_data': processed_data,
            'target_encoder': self.target_encoder,
            'feature_encoders': self.feature_encoders
        }
    
    def evaluate_model_performance(self, model, X_test, y_test):
        """
        Evaluate model performance on test data.
        
        Args:
            model: Trained model
            X_test (pd.DataFrame): Test features
            y_test (array): Test target
            
        Returns:
            dict: Performance metrics
        """
        y_pred = model.predict(X_test)
        
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0),
            'classification_report': classification_report(y_test, y_pred)
        }
        
        return metrics
    
    def get_feature_importance(self, model, feature_names):
        """
        Get feature importance from trained model (if available).
        
        Args:
            model: Trained model
            feature_names (list): Feature names
            
        Returns:
            pd.DataFrame: Feature importance dataframe
        """
        if hasattr(model, 'feature_importances_'):
            importance_df = pd.DataFrame({
                'Feature': feature_names,
                'Importance': model.feature_importances_
            }).sort_values('Importance', ascending=False)
            return importance_df
        else:
            return None
